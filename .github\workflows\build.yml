name: Build Android APK

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y openjdk-8-jdk autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev
        
    - name: Install Python dependencies
      run: |
        pip install buildozer cython
        
    - name: Build APK
      run: |
        buildozer android debug
        
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: twitter-downloader-apk
        path: bin/*.apk
        
    - name: Create Release
      if: github.ref == 'refs/heads/main'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v2.1-${{ github.run_number }}
        name: Twitter Video Downloader Pro v2.1
        body: |
          🎉 Twitter Video Downloader Pro APK
          
          ## Features:
          - ✅ Download Twitter videos in high quality
          - ✅ Support all Twitter URLs (twitter.com, x.com, t.co)
          - ✅ Choose video quality (Best, 1080p, 720p, 480p)
          - ✅ Preview video information
          - ✅ Material Design interface
          - ✅ Auto save to Downloads folder
          
          ## Installation:
          1. Enable "Unknown sources" in Android settings
          2. Download the APK file
          3. Install on your Android device
          
          ## Size: ~50-80 MB
          ## Android: 5.0+
          
        files: bin/*.apk
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
