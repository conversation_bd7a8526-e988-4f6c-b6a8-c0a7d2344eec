#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق الاحترافي
Run Professional Mobile App
"""

import sys
import os

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting Twitter Video Downloader Pro...")
    print("=" * 50)
    
    try:
        # استيراد التطبيق
        from mobile_app_pro import run_mobile_app
        
        # تشغيل التطبيق
        success = run_mobile_app()
        
        if success:
            print("✅ App closed successfully")
        else:
            print("❌ App failed to start")
            return 1
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all files are in the same directory")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
