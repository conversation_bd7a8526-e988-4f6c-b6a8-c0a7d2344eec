@echo off
chcp 65001 >nul
title 🚀 Twitter Video Downloader Pro - Mobile

cls
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █    🚀 Twitter Video Downloader Pro - Mobile Edition         █
echo █           تطبيق تحميل فيديوهات تويتر الاحترافي              █
echo █                                                              █
echo █                    الإصدار 2.0 المحترف                     █
echo █                  Professional Mobile App                    █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 جاري فحص النظام والمتطلبات...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من https://python.org
    echo.
    pause
    exit /b 1
)
echo ✅ Python متاح ومثبت

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متاح
    echo 💡 يرجى إعادة تثبيت Python مع pip
    echo.
    pause
    exit /b 1
)
echo ✅ pip متاح ومثبت

REM التحقق من وجود Kivy
echo 🔍 فحص Kivy...
python -c "import kivy" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت Kivy و KivyMD...
    echo هذا قد يستغرق بضع دقائق...
    pip install kivy kivymd
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Kivy
        echo 💡 جرب: pip install --upgrade pip
        echo 💡 أو: pip install kivy[base] kivymd
        echo.
        pause
        exit /b 1
    )
)
echo ✅ Kivy متاح ومثبت

REM التحقق من وجود KivyMD
echo 🔍 فحص KivyMD...
python -c "import kivymd" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت KivyMD...
    pip install kivymd
    if errorlevel 1 (
        echo ❌ فشل في تثبيت KivyMD
        echo 💡 جرب: pip install --upgrade kivymd
        echo.
        pause
        exit /b 1
    )
)
echo ✅ KivyMD متاح ومثبت

REM التحقق من باقي المتطلبات
echo 📦 تثبيت المتطلبات الإضافية...
if exist requirements_mobile.txt (
    pip install -r requirements_mobile.txt >nul 2>&1
) else (
    pip install yt-dlp requests certifi pillow >nul 2>&1
)

echo.
echo 🎉 جميع المتطلبات جاهزة!
echo.
echo 🚀 تشغيل التطبيق الاحترافي...
echo 💡 سيفتح في نافذة محاكي الموبايل
echo 📱 الواجهة محسنة للاستخدام على الموبايل
echo.

REM تشغيل التطبيق
python mobile_app.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo 💡 تحقق من الأخطاء أعلاه
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. pip install --upgrade kivy kivymd
    echo 2. pip install --upgrade yt-dlp
    echo 3. python -m pip install --upgrade pip
    echo 4. تأكد من وجود جميع الملفات المطلوبة
    echo.
    echo 📞 للدعم الفني:
    echo - تحقق من ملف README_MOBILE.md
    echo - راجع ملف التعليمات_التطبيق_الموبايل.md
    echo.
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح!
    echo 🎉 شكراً لاستخدام Twitter Video Downloader Pro
    echo.
)

echo 👋 شكراً لاستخدام التطبيق الاحترافي!
echo 💡 للحصول على أحدث التحديثات، راجع ملفات التوثيق
echo.
pause
