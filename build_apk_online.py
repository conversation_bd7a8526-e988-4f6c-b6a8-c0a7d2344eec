#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Twitter Video Downloader - Online APK Builder
بناء APK أونلاين باستخدام GitHub Actions
"""

import os
import json
import base64
import requests
from pathlib import Path

def create_github_workflow():
    """إنشاء GitHub Actions workflow لبناء APK"""
    
    workflow_content = """name: Build Android APK

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y openjdk-8-jdk autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev
        
    - name: Install Python dependencies
      run: |
        pip install buildozer cython
        
    - name: Build APK
      run: |
        buildozer android debug
        
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: twitter-downloader-apk
        path: bin/*.apk
        
    - name: Create Release
      if: github.ref == 'refs/heads/main'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v2.1-${{ github.run_number }}
        name: Twitter Video Downloader Pro v2.1
        body: |
          🎉 Twitter Video Downloader Pro APK
          
          ## Features:
          - ✅ Download Twitter videos in high quality
          - ✅ Support all Twitter URLs (twitter.com, x.com, t.co)
          - ✅ Choose video quality (Best, 1080p, 720p, 480p)
          - ✅ Preview video information
          - ✅ Material Design interface
          - ✅ Auto save to Downloads folder
          
          ## Installation:
          1. Enable "Unknown sources" in Android settings
          2. Download the APK file
          3. Install on your Android device
          
          ## Size: ~50-80 MB
          ## Android: 5.0+
          
        files: bin/*.apk
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
"""
    
    return workflow_content

def create_project_files():
    """إنشاء ملفات المشروع للرفع"""
    
    # قراءة محتوى mobile_simple.py
    try:
        with open('mobile_simple.py', 'r', encoding='utf-8') as f:
            mobile_simple_content = f.read()
    except FileNotFoundError:
        print("❌ Error: mobile_simple.py not found")
        return None
    
    # إنشاء main.py
    main_content = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from mobile_simple import main
if __name__ == "__main__":
    main()
"""
    
    # إنشاء buildozer.spec
    buildozer_content = """[app]
title = Twitter Video Downloader Pro
package.name = twitterdownloaderpro
package.domain = com.twitterdownloader.pro
source.dir = .
version = 2.1
requirements = python3,kivy,kivymd,yt-dlp,requests,certifi,pillow
orientation = portrait
fullscreen = 0

[buildozer]
log_level = 2
warn_on_root = 1

[android]
android.permissions = android.permission.INTERNET, android.permission.WRITE_EXTERNAL_STORAGE, android.permission.READ_EXTERNAL_STORAGE
android.arch = arm64-v8a
android.allow_backup = True
"""
    
    # إنشاء README.md
    readme_content = """# Twitter Video Downloader Pro

🎉 Professional mobile app for downloading Twitter videos in high quality.

## Features
- ✅ Download Twitter videos in high quality
- ✅ Support all Twitter URLs (twitter.com, x.com, t.co)
- ✅ Choose video quality (Best, 1080p, 720p, 480p)
- ✅ Preview video information
- ✅ Material Design interface
- ✅ Auto save to Downloads folder

## Installation
1. Download the APK from Releases
2. Enable "Unknown sources" in Android settings
3. Install the APK on your device

## Build from Source
This project uses GitHub Actions to automatically build APK files.

## License
Open Source
"""
    
    return {
        'mobile_simple.py': mobile_simple_content,
        'main.py': main_content,
        'buildozer.spec': buildozer_content,
        'README.md': readme_content,
        '.github/workflows/build.yml': create_github_workflow()
    }

def create_github_repo():
    """إنشاء مستودع GitHub وتحميل الملفات"""
    
    print("🚀 Creating GitHub repository for APK building...")
    print("📁 Preparing project files...")
    
    files = create_project_files()
    if not files:
        return False
    
    # حفظ الملفات محلياً
    os.makedirs('.github/workflows', exist_ok=True)
    
    for filename, content in files.items():
        filepath = Path(filename)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Created: {filename}")
    
    print("\n🎉 Project files created successfully!")
    print("\n📋 Next steps:")
    print("1. Create a new GitHub repository")
    print("2. Upload these files to the repository:")
    for filename in files.keys():
        print(f"   - {filename}")
    print("3. GitHub Actions will automatically build the APK")
    print("4. Download APK from the Releases section")
    
    return True

def create_colab_notebook():
    """إنشاء Google Colab notebook لبناء APK"""
    
    notebook_content = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# 🚀 Twitter Video Downloader Pro - APK Builder\n",
                    "\n",
                    "This notebook builds an Android APK for the Twitter Video Downloader Pro app.\n",
                    "\n",
                    "## Steps:\n",
                    "1. Install dependencies\n",
                    "2. Upload project files\n",
                    "3. Build APK\n",
                    "4. Download APK"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Install system dependencies\n",
                    "!apt-get update\n",
                    "!apt-get install -y openjdk-8-jdk autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev\n",
                    "\n",
                    "# Install Python dependencies\n",
                    "!pip install buildozer cython\n",
                    "\n",
                    "print('✅ Dependencies installed successfully!')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Upload project files\n",
                    "from google.colab import files\n",
                    "\n",
                    "print('📁 Upload mobile_simple.py:')\n",
                    "uploaded = files.upload()\n",
                    "\n",
                    "print('📁 Upload main.py:')\n",
                    "uploaded = files.upload()\n",
                    "\n",
                    "print('📁 Upload buildozer.spec:')\n",
                    "uploaded = files.upload()\n",
                    "\n",
                    "print('✅ All files uploaded!')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Build APK\n",
                    "print('🏗️ Building APK... This may take 30-60 minutes')\n",
                    "!buildozer android debug\n",
                    "\n",
                    "print('✅ APK built successfully!')\n",
                    "!ls -la bin/"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Download APK\n",
                    "import os\n",
                    "from google.colab import files\n",
                    "\n",
                    "apk_files = [f for f in os.listdir('bin/') if f.endswith('.apk')]\n",
                    "\n",
                    "if apk_files:\n",
                    "    apk_file = apk_files[0]\n",
                    "    print(f'📱 Downloading: {apk_file}')\n",
                    "    files.download(f'bin/{apk_file}')\n",
                    "    print('✅ APK downloaded successfully!')\n",
                    "else:\n",
                    "    print('❌ No APK file found')"
                ]
            }
        ],
        "metadata": {
            "colab": {
                "name": "Twitter_Video_Downloader_APK_Builder.ipynb",
                "provenance": []
            },
            "kernelspec": {
                "display_name": "Python 3",
                "name": "python3"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 0
    }
    
    with open('Twitter_Video_Downloader_APK_Builder.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook_content, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: Twitter_Video_Downloader_APK_Builder.ipynb")
    print("📋 Upload this notebook to Google Colab to build APK online")

def main():
    """الدالة الرئيسية"""
    print("🚀 Twitter Video Downloader Pro - APK Builder")
    print("=" * 50)
    
    print("\n🔧 Creating project files for APK building...")
    
    # إنشاء ملفات المشروع
    success = create_github_repo()
    
    if success:
        print("\n📓 Creating Google Colab notebook...")
        create_colab_notebook()
        
        print("\n🎉 APK building setup completed!")
        print("\n📋 Choose your preferred method:")
        print("1. 🌐 GitHub Actions (Automatic)")
        print("   - Upload files to GitHub repository")
        print("   - GitHub will build APK automatically")
        print("   - Download from Releases section")
        print("\n2. ☁️ Google Colab (Manual)")
        print("   - Upload Twitter_Video_Downloader_APK_Builder.ipynb to Colab")
        print("   - Run all cells step by step")
        print("   - Download APK directly")
        
        print("\n🎯 Expected APK:")
        print("   - Name: twitterdownloaderpro-2.1-arm64-v8a-debug.apk")
        print("   - Size: ~50-80 MB")
        print("   - Platform: Android 5.0+")
        
        return True
    else:
        print("❌ Failed to create project files")
        return False

if __name__ == "__main__":
    main()
