# برنامج تحميل فيديوهات تويتر
# Twitter Video Downloader

برنامج بسيط وفعال لتحميل الفيديوهات من تويتر (X) بجودة عالية باستخدام Python.

## المميزات

- ✅ تحميل فيديوهات من تويتر/X بجودات مختلفة
- ✅ واجهة رسومية سهلة الاستخدام
- ✅ واجهة سطر أوامر للمستخدمين المتقدمين
- ✅ معاينة معلومات الفيديو قبل التحميل
- ✅ اختيار مجلد التحميل
- ✅ متابعة تقدم التحميل
- ✅ دعم الروابط المختصرة (t.co)
- ✅ دعم الروابط الجديدة (x.com)

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

## التثبيت

1. تأكد من تثبيت Python على جهازك
2. قم بتحميل ملفات البرنامج
3. افتح موجه الأوامر في مجلد البرنامج
4. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

## طريقة الاستخدام

### 1. الواجهة الرسومية (مستحسن للمبتدئين)

```bash
python main.py --gui
```

أو ببساطة:

```bash
python main.py
```

ثم اختر الخيار رقم 1

### 2. سطر الأوامر

```bash
python main.py --cli
```

أو اختر الخيار رقم 2 من القائمة الرئيسية

### 3. استخدام مباشر

```bash
python twitter_downloader.py
```

## خيارات الجودة المتاحة

- **أفضل جودة متاحة**: يحمل أعلى جودة موجودة
- **1080p**: جودة عالية (Full HD)
- **720p**: جودة عالية (HD)
- **480p**: جودة متوسطة
- **360p**: جودة منخفضة

## أمثلة على الروابط المدعومة

```
https://twitter.com/username/status/1234567890
https://x.com/username/status/1234567890
https://t.co/abcdefghij
```

## هيكل المشروع

```
├── main.py                 # الملف الرئيسي
├── twitter_downloader.py   # محرك التحميل
├── gui.py                  # الواجهة الرسومية
├── config.py               # إعدادات البرنامج
├── requirements.txt        # المكتبات المطلوبة
└── README.md              # هذا الملف
```

## استكشاف الأخطاء

### مشكلة: "ModuleNotFoundError"
**الحل**: تأكد من تثبيت جميع المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

### مشكلة: "رابط غير صحيح"
**الحل**: تأكد من أن الرابط يحتوي على فيديو وليس صورة فقط

### مشكلة: فشل التحميل
**الحل**: 
- تحقق من اتصال الإنترنت
- تأكد من أن الفيديو متاح للعامة
- جرب جودة أقل

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تحديث yt-dlp إلى أحدث إصدار
2. تحقق من صحة الرابط
3. جرب إعادة تشغيل البرنامج

## تحديث البرنامج

لتحديث مكتبة yt-dlp (مستحسن بشكل دوري):

```bash
pip install --upgrade yt-dlp
```

## ملاحظات مهمة

- البرنامج يستخدم مكتبة `yt-dlp` المفتوحة المصدر
- يتم حفظ الفيديوهات في مجلد "Downloads/Twitter_Videos" افتراضياً
- البرنامج يحترم حقوق الطبع والنشر - استخدمه بمسؤولية
- لا يدعم تحميل المحتوى المحمي أو الخاص

## الترخيص

هذا البرنامج مجاني ومفتوح المصدر للاستخدام الشخصي.

---

**تطوير**: برنامج تحميل فيديوهات تويتر  
**الإصدار**: 1.0  
**التاريخ**: 2024
