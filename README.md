# 🎬 برنامج تحميل فيديوهات تويتر المحسن
# Enhanced Twitter Video Downloader

برنامج متطور وسهل الاستخدام لتحميل الفيديوهات من تويتر (X) بجودة عالية باستخدام Python.

## ✨ المميزات الجديدة

- 🎯 **واجهة رسومية محسنة** مع تبويبات منظمة
- 🔍 **اكتشاف تلقائي للروابط** من الحافظة
- 📋 **معاينة شاملة** لمعلومات الفيديو
- ⚡ **تحميل سريع** بجودات متعددة
- 📂 **إدارة مجلدات** سهلة ومرنة
- 🛑 **إيقاف التحميل** في أي وقت
- 🎨 **واجهة عربية** جميلة وسهلة
- 🔄 **مراقبة الحافظة** التلقائية
- 📊 **شريط تقدم** تفاعلي
- 💡 **نصائح ومساعدة** مدمجة

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

## 🚀 التثبيت والتشغيل السريع

### الطريقة الأسهل (للمبتدئين):
1. انقر مرتين على ملف `تشغيل_البرنامج.bat`
2. سيتم تثبيت كل شيء تلقائياً وتشغيل البرنامج

### التثبيت اليدوي:
1. تأكد من تثبيت Python 3.7+ على جهازك
2. قم بتحميل ملفات البرنامج
3. افتح موجه الأوامر في مجلد البرنامج
4. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

## 🎮 طرق التشغيل

### 1. 🖥️ الواجهة الرسومية (الأفضل)

```bash
# الطريقة الأسهل
تشغيل_البرنامج.bat

# أو
python gui.py

# أو
python main.py --gui
```

### 2. 💻 سطر الأوامر

```bash
python twitter_downloader.py

# أو
python main.py --cli
```

### 3. 📋 القائمة التفاعلية

```bash
python main.py
```

## خيارات الجودة المتاحة

- **أفضل جودة متاحة**: يحمل أعلى جودة موجودة
- **1080p**: جودة عالية (Full HD)
- **720p**: جودة عالية (HD)
- **480p**: جودة متوسطة
- **360p**: جودة منخفضة

## أمثلة على الروابط المدعومة

```
https://twitter.com/username/status/1234567890
https://x.com/username/status/1234567890
https://t.co/abcdefghij
```

## هيكل المشروع

```
├── main.py                 # الملف الرئيسي
├── twitter_downloader.py   # محرك التحميل
├── gui.py                  # الواجهة الرسومية
├── config.py               # إعدادات البرنامج
├── requirements.txt        # المكتبات المطلوبة
└── README.md              # هذا الملف
```

## استكشاف الأخطاء

### مشكلة: "ModuleNotFoundError"
**الحل**: تأكد من تثبيت جميع المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

### مشكلة: "رابط غير صحيح"
**الحل**: تأكد من أن الرابط يحتوي على فيديو وليس صورة فقط

### مشكلة: فشل التحميل
**الحل**: 
- تحقق من اتصال الإنترنت
- تأكد من أن الفيديو متاح للعامة
- جرب جودة أقل

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تحديث yt-dlp إلى أحدث إصدار
2. تحقق من صحة الرابط
3. جرب إعادة تشغيل البرنامج

## تحديث البرنامج

لتحديث مكتبة yt-dlp (مستحسن بشكل دوري):

```bash
pip install --upgrade yt-dlp
```

## ملاحظات مهمة

- البرنامج يستخدم مكتبة `yt-dlp` المفتوحة المصدر
- يتم حفظ الفيديوهات في مجلد "Downloads/Twitter_Videos" افتراضياً
- البرنامج يحترم حقوق الطبع والنشر - استخدمه بمسؤولية
- لا يدعم تحميل المحتوى المحمي أو الخاص

## الترخيص

هذا البرنامج مجاني ومفتوح المصدر للاستخدام الشخصي.

---

**تطوير**: برنامج تحميل فيديوهات تويتر  
**الإصدار**: 1.0  
**التاريخ**: 2024
