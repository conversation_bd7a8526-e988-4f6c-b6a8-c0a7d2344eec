#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الملف الرئيسي لبرنامج تحميل فيديوهات تويتر
Twitter Video Downloader - Main File
"""

import sys
import os
from gui import main as gui_main
from twitter_downloader import main as cli_main

def show_menu():
    """عرض قائمة الخيارات"""
    print("=" * 50)
    print("برنامج تحميل فيديوهات تويتر")
    print("Twitter Video Downloader")
    print("=" * 50)
    print("1. تشغيل الواجهة الرسومية (GUI)")
    print("2. تشغيل سطر الأوامر (CLI)")
    print("3. خروج")
    print("=" * 50)

def main():
    """الدالة الرئيسية"""
    try:
        if len(sys.argv) > 1:
            if sys.argv[1] == "--gui":
                gui_main()
            elif sys.argv[1] == "--cli":
                cli_main()
            elif sys.argv[1] == "--help":
                print("استخدام البرنامج:")
                print("python main.py --gui    # تشغيل الواجهة الرسومية")
                print("python main.py --cli    # تشغيل سطر الأوامر")
                print("python main.py          # عرض القائمة")
            else:
                print("خيار غير صحيح. استخدم --help لعرض المساعدة")
        else:
            while True:
                show_menu()
                choice = input("اختر رقم الخيار: ").strip()
                
                if choice == "1":
                    print("تشغيل الواجهة الرسومية...")
                    gui_main()
                    break
                elif choice == "2":
                    print("تشغيل سطر الأوامر...")
                    cli_main()
                    break
                elif choice == "3":
                    print("شكراً لاستخدام البرنامج!")
                    break
                else:
                    print("خيار غير صحيح، يرجى المحاولة مرة أخرى.")
                    input("اضغط Enter للمتابعة...")
                    os.system('cls' if os.name == 'nt' else 'clear')
    
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم.")
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    main()
