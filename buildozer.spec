[app]

# (str) Title of your application
title = Twitter Video Downloader

# (str) Package name
package.name = twitterdownloader

# (str) Package domain (needed for android/ios packaging)
package.domain = com.twitterdownloader.app

# (str) Source code where the main.py live
source.dir = .

# (list) Source files to include (let empty to include all the files)
source.include_exts = py,png,jpg,kv,atlas,json,txt

# (str) Application versioning (method 1)
version = 1.0

# (list) Application requirements
requirements = python3,kivy,kivymd,yt-dlp,requests,certifi,pillow

# (str) Presplash of the application
#presplash.filename = %(source.dir)s/data/presplash.png

# (str) Icon of the application
#icon.filename = %(source.dir)s/data/icon.png

# (str) Supported orientation (landscape, portrait or all)
orientation = portrait

# (bool) Indicate if the application should be fullscreen or not
fullscreen = 0

[buildozer]

# (int) Log level (0 = error only, 1 = info, 2 = debug (with command output))
log_level = 2

# (int) Display warning if buildozer is run as root (0 = False, 1 = True)
warn_on_root = 1

[android]

# (str) Android entry point, default is ok for Kivy-based app
#android.entrypoint = org.kivy.android.PythonActivity

# (list) Pattern to whitelist for the whole project
#android.whitelist =

# (str) Android app theme, default is ok for Kivy-based app
#android.theme = "@android:style/Theme.NoTitleBar"

# (list) Android application meta-data to set (key=value format)
#android.meta_data =

# (list) Android library project to add (will be added in the
# project.properties automatically.)
#android.library_references = @jar/your-lib-1.0.jar

# (str) Android logcat filters to use
#android.logcat_filters = *:S python:D

# (bool) Copy library instead of making a libpymodules.so
#android.copy_libs = 1

# (str) The Android arch to build for, choices: armeabi-v7a, arm64-v8a, x86, x86_64
android.arch = arm64-v8a

# (int) overrides automatic versionCode computation (used in build.gradle)
# this is not the same as app version and should only be edited if you know what you're doing
# android.numeric_version = 1

# (bool) enables Android auto backup feature (Android API >=23)
android.allow_backup = True

# (str) XML file for custom backup rules (see official auto backup documentation)
# android.backup_rules =

# (str) If you need to insert variables into your AndroidManifest.xml file,
# you can do so with the manifestPlaceholders property.
# This property takes a map of key-value pairs. (via a string)
# Usage example : android.manifest_placeholders = key:value, key2:value2
# android.manifest_placeholders = 

# (bool) Skip byte compile for .py files
# android.no-byte-compile-python = False

# (str) The format used to package the app for release mode (aab or apk).
# android.release_artifact = aab

[android.permissions]
# (list) Permissions which will be requested from the user
android.permissions = INTERNET,WRITE_EXTERNAL_STORAGE,READ_EXTERNAL_STORAGE

[android.gradle_dependencies]
# (list) Gradle dependencies to add 
#android.gradle_dependencies = 

[android.gradle_repositories]
# (list) Gradle repositories to add
#android.gradle_repositories = 

[android.add_src]
# (list) Android additional libraries to copy into libs/armeabi
#android.add_src = 

[android.add_aars]
# (list) Android AAR archives to add
#android.add_aars = 

[android.add_jars]
# (list) Android JAR files to add to the libs so they can be imported in Java code
#android.add_jars = 

[android.add_assets]
# (list) Assets to add to the apk
#android.add_assets = 

[android.add_compile_options]
# (list) Java compile options
# android.add_compile_options = "sourceCompatibility = 1.8", "targetCompatibility = 1.8"

[android.add_gradle_repositories]
# (list) Gradle repositories to add (can be local or remote)
#android.add_gradle_repositories = 

[android.add_packaging_options]
# (list) packaging options to add 
# see https://google.github.io/android-gradle-dsl/current/com.android.build.gradle.internal.dsl.PackagingOptions.html
# android.add_packaging_options = "exclude 'META-INF/DEPENDENCIES'", "exclude 'META-INF/LICENSE'", "exclude 'META-INF/LICENSE.txt'", "exclude 'META-INF/license.txt'", "exclude 'META-INF/NOTICE'", "exclude 'META-INF/NOTICE.txt'", "exclude 'META-INF/notice.txt'", "exclude 'META-INF/ASL2.0'"

[android.add_gradle_repositories]
# (list) Gradle repositories to add
#android.add_gradle_repositories = 

[android.add_java_src]
# (list) Java files to add to the android project (can be java or a directory containing the files)
#android.add_java_src = 

[android.add_compile_options]
# (list) Java compile options
# android.add_compile_options = "sourceCompatibility = 1.8", "targetCompatibility = 1.8"

[android.add_gradle_repositories]
# (list) Gradle repositories to add (can be local or remote)
#android.add_gradle_repositories = 

[android.add_packaging_options]
# (list) packaging options to add 
# see https://google.github.io/android-gradle-dsl/current/com.android.build.gradle.internal.dsl.PackagingOptions.html
# android.add_packaging_options = "exclude 'META-INF/DEPENDENCIES'", "exclude 'META-INF/LICENSE'", "exclude 'META-INF/LICENSE.txt'", "exclude 'META-INF/license.txt'", "exclude 'META-INF/NOTICE'", "exclude 'META-INF/NOTICE.txt'", "exclude 'META-INF/notice.txt'", "exclude 'META-INF/ASL2.0'"
