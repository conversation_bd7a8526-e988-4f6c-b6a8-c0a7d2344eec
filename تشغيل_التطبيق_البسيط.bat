@echo off
chcp 65001 >nul
title 🚀 Twitter Video Downloader - Simple Mobile

cls
color 0A
echo.
echo ████████████████████████████████████████████████████████
echo █                                                      █
echo █    🚀 Twitter Video Downloader - Simple Mobile       █
echo █           تطبيق تحميل فيديوهات تويتر البسيط           █
echo █                                                      █
echo █                  نسخة بسيطة بدون أخطاء                █
echo █                Simple Version No Errors             █
echo █                                                      █
echo ████████████████████████████████████████████████████████
echo.

echo 🔍 جاري فحص النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo 💡 قم بتثبيت Python من https://python.org
    pause
    exit /b 1
)
echo ✅ Python متاح

REM التحقق من وجود Kivy
echo 🔍 فحص Kivy...
python -c "import kivy" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت Kivy...
    pip install kivy kivymd
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Kivy
        pause
        exit /b 1
    )
)
echo ✅ Kivy متاح

echo.
echo 🚀 تشغيل التطبيق البسيط...
echo 💡 هذه نسخة بسيطة للاختبار
echo 📱 الواجهة محسنة للموبايل
echo.

python mobile_simple.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo 💡 تحقق من الأخطاء أعلاه
    echo.
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
)

echo.
echo 👋 شكراً لاستخدام التطبيق البسيط!
pause
