name: Build Android APK

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y openjdk-8-jdk autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev
        
    - name: Install Python dependencies
      run: |
        pip install buildozer cython
        
    - name: Cache buildozer global directory
      uses: actions/cache@v3
      with:
        path: ~/.buildozer
        key: ${{ runner.os }}-buildozer-${{ hashFiles('buildozer.spec') }}
        
    - name: Cache buildozer directory
      uses: actions/cache@v3
      with:
        path: .buildozer
        key: ${{ runner.os }}-buildozer-build-${{ hashFiles('buildozer.spec') }}
        
    - name: Build APK
      run: |
        buildozer android debug
        
    - name: Upload APK as artifact
      uses: actions/upload-artifact@v3
      with:
        name: twitter-downloader-apk
        path: bin/*.apk
        retention-days: 30
        
    - name: Get APK info
      id: apk_info
      run: |
        APK_FILE=$(ls bin/*.apk | head -1)
        APK_SIZE=$(du -h "$APK_FILE" | cut -f1)
        echo "apk_file=$APK_FILE" >> $GITHUB_OUTPUT
        echo "apk_size=$APK_SIZE" >> $GITHUB_OUTPUT
        echo "apk_name=$(basename $APK_FILE)" >> $GITHUB_OUTPUT
        
    - name: Create Release
      if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v2.1-${{ github.run_number }}
        name: Twitter Video Downloader Pro v2.1 Build ${{ github.run_number }}
        body: |
          # 🎉 Twitter Video Downloader Pro APK
          
          **Version:** 2.1  
          **Build:** ${{ github.run_number }}  
          **Size:** ${{ steps.apk_info.outputs.apk_size }}  
          **Platform:** Android 5.0+  
          **Architecture:** arm64-v8a  
          
          ## ✨ Features:
          - ✅ Download Twitter videos in high quality
          - ✅ Support all Twitter URLs (twitter.com, x.com, t.co)
          - ✅ Choose video quality (Best, 1080p, 720p, 480p)
          - ✅ Preview video information before download
          - ✅ Beautiful Material Design interface
          - ✅ Auto save to Downloads folder
          - ✅ Real-time progress bar
          - ✅ Clear status messages
          
          ## 📱 Installation:
          1. **Enable "Unknown sources"** in Android settings
          2. **Download the APK** file below
          3. **Install** on your Android device
          4. **Enjoy** downloading Twitter videos!
          
          ## 🔒 Security:
          - ✅ Open source code
          - ✅ No data collection
          - ✅ Local processing only
          - ✅ Minimal permissions (Internet + Storage)
          
          ## 📋 Supported URLs:
          - `https://twitter.com/username/status/123456789`
          - `https://x.com/username/status/123456789`
          - `https://t.co/abcdefghij`
          - `https://mobile.twitter.com/username/status/123456789`
          
          ---
          
          **📱 APK File:** `${{ steps.apk_info.outputs.apk_name }}`  
          **🔗 Download:** Click the APK file below to download
          
        files: bin/*.apk
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
