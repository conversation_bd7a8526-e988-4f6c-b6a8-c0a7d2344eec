#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للواجهة الرسومية
Quick GUI Test
"""

import sys
import os

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        print("🔍 اختبار استيراد المكتبات...")
        
        import tkinter as tk
        print("✅ tkinter - OK")
        
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter modules - OK")
        
        import yt_dlp
        print("✅ yt-dlp - OK")
        
        from twitter_downloader import TwitterVideoDownloader
        print("✅ twitter_downloader - OK")
        
        import config
        print("✅ config - OK")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    try:
        print("\n🖥️ اختبار الواجهة الرسومية...")
        
        from gui import TwitterDownloaderGUI
        import tkinter as tk
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار إنشاء الكائن
        app = TwitterDownloaderGUI(root)
        print("✅ إنشاء الواجهة - OK")
        
        # اختبار المتغيرات
        assert hasattr(app, 'url_var'), "url_var غير موجود"
        assert hasattr(app, 'status_var'), "status_var غير موجود"
        assert hasattr(app, 'quality_var'), "quality_var غير موجود"
        assert hasattr(app, 'download_path_var'), "download_path_var غير موجود"
        print("✅ المتغيرات - OK")
        
        # اختبار الدوال
        assert hasattr(app, 'start_download'), "start_download غير موجود"
        assert hasattr(app, 'preview_video'), "preview_video غير موجود"
        assert hasattr(app, 'update_progress'), "update_progress غير موجود"
        print("✅ الدوال - OK")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار برنامج تحميل فيديوهات تويتر")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل اختبار الاستيرادات")
        return False
    
    # اختبار الواجهة
    if not test_gui():
        print("\n❌ فشل اختبار الواجهة")
        return False
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("✅ البرنامج جاهز للاستخدام")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
