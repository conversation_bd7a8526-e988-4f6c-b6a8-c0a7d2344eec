#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Twitter Video Downloader Pro - Mobile App
تطبيق تحميل فيديوهات تويتر الاحترافي للموبايل
"""

import os
import sys
import threading
import json
from pathlib import Path

# التحقق من توفر المكتبات
try:
    from kivy.app import App
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.label import Label
    from kivy.uix.textinput import TextInput
    from kivy.uix.button import Button
    from kivy.uix.progressbar import ProgressBar
    from kivy.uix.popup import Popup
    from kivy.uix.scrollview import ScrollView
    from kivy.clock import Clock
    from kivy.metrics import dp
    from kivy.core.window import Window
    from kivy.uix.gridlayout import GridLayout
    
    # KivyMD للتصميم الجميل
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
    from kivymd.uix.textfield import MDTextField
    from kivymd.uix.label import MDLabel
    from kivymd.uix.card import MDCard
    from kivymd.uix.toolbar import MDTopAppBar
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.snackbar import Snackbar
    from kivymd.uix.progressbar import MDProgressBar
    
    KIVY_AVAILABLE = True
    print("✅ Kivy and KivyMD loaded successfully")
    
except ImportError as e:
    KIVY_AVAILABLE = False
    print(f"❌ Error loading Kivy: {e}")
    print("💡 Install with: pip install kivy kivymd")

# استيراد محرك التحميل
try:
    from twitter_downloader import TwitterVideoDownloader
    import config
    DOWNLOADER_AVAILABLE = True
    print("✅ Twitter downloader loaded successfully")
except ImportError:
    DOWNLOADER_AVAILABLE = False
    print("❌ Twitter downloader not found")

class TwitterDownloaderMobileApp(MDApp):
    """تطبيق تحميل فيديوهات تويتر للموبايل"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Twitter Video Downloader Pro"
        
        # إعداد المظهر
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Orange"
        
        # متغيرات التطبيق
        self.downloader = None
        self.is_downloading = False
        self.download_thread = None
        self.current_quality = "Best Quality"
        self.quality_options = ["Best Quality", "1080p", "720p", "480p"]
        self.quality_index = 0
        
        # عناصر الواجهة
        self.url_input = None
        self.quality_button = None
        self.preview_button = None
        self.download_button = None
        self.stop_button = None
        self.progress_bar = None
        self.status_label = None
        self.info_label = None
        
        print("✅ App initialized successfully")

    def build(self):
        """بناء واجهة التطبيق"""
        try:
            print("🔨 Building app interface...")
            
            # إنشاء محرك التحميل
            if DOWNLOADER_AVAILABLE:
                self.downloader = TwitterVideoDownloader()
                self.downloader.set_progress_callback(self.update_progress)
                print("✅ Downloader engine created")
            
            # تعيين حجم النافذة
            Window.size = (400, 700)
            Window.clearcolor = (0.95, 0.95, 0.95, 1)
            
            # إنشاء الواجهة الرئيسية
            main_screen = self.create_main_interface()
            print("✅ Main interface created")
            
            return main_screen
            
        except Exception as e:
            print(f"❌ Error building app: {e}")
            return self.create_error_screen(str(e))
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الحاوية الرئيسية
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=dp(20)
        )
        
        # شريط العنوان
        toolbar = MDTopAppBar(
            title="Twitter Video Downloader Pro",
            elevation=3,
            md_bg_color=self.theme_cls.primary_color
        )
        
        # منطقة المحتوى مع التمرير
        scroll = ScrollView()
        content_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            adaptive_height=True,
            padding=dp(10)
        )
        
        # إضافة البطاقات
        content_layout.add_widget(self.create_welcome_card())
        content_layout.add_widget(self.create_url_input_card())
        content_layout.add_widget(self.create_settings_card())
        content_layout.add_widget(self.create_info_card())
        content_layout.add_widget(self.create_download_card())
        
        # تجميع العناصر
        scroll.add_widget(content_layout)
        
        root_layout = MDBoxLayout(orientation='vertical')
        root_layout.add_widget(toolbar)
        root_layout.add_widget(scroll)
        
        return root_layout
    
    def create_welcome_card(self):
        """بطاقة الترحيب"""
        card = MDCard(
            orientation='vertical',
            padding=dp(20),
            spacing=dp(10),
            elevation=3,
            size_hint_y=None,
            height=dp(100),
            md_bg_color=self.theme_cls.primary_color
        )
        
        title = MDLabel(
            text="Twitter Video Downloader Pro",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="H5",
            bold=True,
            halign="center"
        )
        
        subtitle = MDLabel(
            text="Download your favorite videos in high quality",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 0.8),
            font_style="Body1",
            halign="center"
        )
        
        card.add_widget(title)
        card.add_widget(subtitle)
        
        return card
    
    def create_url_input_card(self):
        """بطاقة إدخال الرابط"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(140)
        )
        
        # عنوان
        title = MDLabel(
            text="Video URL",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)
        
        # حقل الإدخال
        self.url_input = MDTextField(
            hint_text="Paste Twitter video URL here...",
            helper_text="Supports twitter.com, x.com, t.co",
            helper_text_mode="on_focus",
            multiline=False,
            size_hint_y=None,
            height=dp(50)
        )
        card.add_widget(self.url_input)
        
        # أزرار
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(10),
            size_hint_y=None,
            height=dp(40)
        )
        
        paste_btn = MDRaisedButton(
            text="Paste",
            size_hint_x=None,
            width=dp(80),
            on_release=self.paste_url
        )
        
        clear_btn = MDRaisedButton(
            text="Clear",
            size_hint_x=None,
            width=dp(80),
            md_bg_color=self.theme_cls.accent_color,
            on_release=self.clear_url
        )
        
        self.preview_button = MDRaisedButton(
            text="Preview",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.preview_video
        )
        
        buttons_layout.add_widget(paste_btn)
        buttons_layout.add_widget(clear_btn)
        buttons_layout.add_widget(self.preview_button)
        
        card.add_widget(buttons_layout)
        
        return card

    def create_settings_card(self):
        """بطاقة الإعدادات"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(80)
        )

        # عنوان
        title = MDLabel(
            text="Settings",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)

        # إعدادات
        settings_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(10)
        )

        quality_label = MDLabel(
            text="Quality:",
            size_hint_x=None,
            width=dp(60)
        )

        self.quality_button = MDRaisedButton(
            text=self.current_quality,
            size_hint_x=None,
            width=dp(120),
            on_release=self.cycle_quality
        )

        settings_layout.add_widget(quality_label)
        settings_layout.add_widget(self.quality_button)

        card.add_widget(settings_layout)

        return card

    def create_info_card(self):
        """بطاقة المعلومات"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(150)
        )

        # عنوان
        title = MDLabel(
            text="Video Information",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)

        # منطقة المعلومات
        info_scroll = ScrollView()
        self.info_label = MDLabel(
            text="No information available",
            theme_text_color="Secondary",
            text_size=(None, None),
            halign="left"
        )
        info_scroll.add_widget(self.info_label)
        card.add_widget(info_scroll)

        return card

    def create_download_card(self):
        """بطاقة التحميل"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(120)
        )

        # عنوان
        title = MDLabel(
            text="Download",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)

        # شريط التقدم
        self.progress_bar = MDProgressBar(
            size_hint_y=None,
            height=dp(8)
        )
        card.add_widget(self.progress_bar)

        # حالة التحميل
        self.status_label = MDLabel(
            text="Ready to download",
            theme_text_color="Secondary",
            halign="center"
        )
        card.add_widget(self.status_label)

        # أزرار التحكم
        control_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(10),
            size_hint_y=None,
            height=dp(40)
        )

        self.download_button = MDRaisedButton(
            text="Download",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.start_download
        )

        self.stop_button = MDRaisedButton(
            text="Stop",
            md_bg_color=(0.8, 0.2, 0.2, 1),
            disabled=True,
            on_release=self.stop_download
        )

        control_layout.add_widget(self.download_button)
        control_layout.add_widget(self.stop_button)

        card.add_widget(control_layout)

        return card

    def create_error_screen(self, error_message):
        """شاشة الخطأ"""
        layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            padding=dp(20)
        )

        error_label = MDLabel(
            text=f"Error: {error_message}",
            theme_text_color="Error",
            halign="center"
        )

        retry_button = MDRaisedButton(
            text="Retry",
            size_hint=(None, None),
            size=(dp(100), dp(40)),
            pos_hint={'center_x': 0.5},
            on_release=lambda x: self.restart_app()
        )

        layout.add_widget(error_label)
        layout.add_widget(retry_button)

        return layout

    # الدوال الوظيفية
    def paste_url(self, instance):
        """لصق الرابط من الحافظة"""
        try:
            from kivy.core.clipboard import Clipboard
            clipboard_content = Clipboard.paste()

            if clipboard_content and clipboard_content.startswith(('http://', 'https://')):
                self.url_input.text = clipboard_content.strip()
                self.show_message("URL pasted successfully")
            else:
                self.show_message("No valid URL in clipboard")
        except Exception as e:
            self.show_message(f"Paste error: {e}")

    def clear_url(self, instance):
        """مسح الرابط"""
        self.url_input.text = ""
        self.info_label.text = "No information available"
        self.show_message("URL cleared")

    def cycle_quality(self, instance):
        """تبديل الجودة"""
        self.quality_index = (self.quality_index + 1) % len(self.quality_options)
        self.current_quality = self.quality_options[self.quality_index]
        self.quality_button.text = self.current_quality
        self.show_message(f"Quality: {self.current_quality}")

    def preview_video(self, instance):
        """معاينة الفيديو"""
        url = self.url_input.text.strip()
        if not url:
            self.show_message("Please enter a video URL")
            return

        if not DOWNLOADER_AVAILABLE:
            self.show_message("Downloader not available")
            return

        self.status_label.text = "Getting video information..."
        self.progress_bar.start()

        # تشغيل في خيط منفصل
        threading.Thread(target=self._preview_thread, args=(url,), daemon=True).start()

    def _preview_thread(self, url):
        """خيط معاينة الفيديو"""
        try:
            info, error = self.downloader.get_video_info(url)
            Clock.schedule_once(lambda dt: self._update_preview_ui(info, error), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_preview_ui(None, str(e)), 0)

    def _update_preview_ui(self, info, error):
        """تحديث واجهة المعاينة"""
        self.progress_bar.stop()

        if error:
            self.status_label.text = f"Error: {error}"
            self.info_label.text = f"❌ Error: {error}"
        else:
            self.status_label.text = "Information retrieved"
            info_text = f"""📝 Title: {info.get('title', 'N/A')}
👤 Uploader: {info.get('uploader', 'N/A')}
⏱️ Duration: {info.get('duration', 'N/A')} seconds
👀 Views: {info.get('view_count', 'N/A'):,}
❤️ Likes: {info.get('like_count', 'N/A'):,}
📅 Upload Date: {info.get('upload_date', 'N/A')}

📄 Description:
{info.get('description', 'No description')[:200] if info.get('description') else 'No description'}..."""

            self.info_label.text = info_text
            self.info_label.text_size = (dp(300), None)

    def start_download(self, instance):
        """بدء التحميل"""
        url = self.url_input.text.strip()
        if not url:
            self.show_message("Please enter a video URL")
            return

        if not DOWNLOADER_AVAILABLE:
            self.show_message("Downloader not available")
            return

        if self.is_downloading:
            self.show_message("Already downloading")
            return

        # تحديث الواجهة
        self.download_button.disabled = True
        self.stop_button.disabled = False
        self.is_downloading = True
        self.progress_bar.start()
        self.status_label.text = "Downloading..."

        # إعداد الجودة
        quality_map = {
            "Best Quality": "best",
            "1080p": "best[height<=1080]",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]"
        }

        quality = quality_map.get(self.current_quality, "best")
        self.downloader.set_quality(quality)

        # تحديد مسار التحميل
        download_path = self.get_download_path()
        self.downloader.set_download_path(download_path)

        # بدء التحميل
        self.download_thread = threading.Thread(target=self._download_thread, args=(url,), daemon=True)
        self.download_thread.start()

    def _download_thread(self, url):
        """خيط التحميل"""
        try:
            success, message = self.downloader.download_video(url)
            Clock.schedule_once(lambda dt: self._update_download_ui(success, message), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_download_ui(False, str(e)), 0)

    def _update_download_ui(self, success, message):
        """تحديث واجهة التحميل"""
        self.progress_bar.stop()
        self.download_button.disabled = False
        self.stop_button.disabled = True
        self.is_downloading = False

        if success:
            self.status_label.text = "✅ Download completed!"
            self.show_success_dialog("Download Successful!", message)
        else:
            self.status_label.text = "❌ Download failed"
            self.show_error_dialog("Download Failed", message)

    def stop_download(self, instance):
        """إيقاف التحميل"""
        self.is_downloading = False
        self.download_button.disabled = False
        self.stop_button.disabled = True
        self.progress_bar.stop()
        self.status_label.text = "Download stopped"
        self.show_message("Download stopped")

    # الدوال المساعدة
    def get_download_path(self):
        """الحصول على مسار التحميل"""
        try:
            # محاولة استخدام مجلد التحميلات في الأندرويد
            from android.storage import primary_external_storage_path
            download_path = os.path.join(primary_external_storage_path(), "Download", "TwitterVideos")
        except ImportError:
            # للاختبار على الكمبيوتر
            download_path = os.path.join(os.path.expanduser("~"), "Downloads", "TwitterVideos")

        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(download_path, exist_ok=True)
        return download_path

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', message), 0)

    def show_message(self, message):
        """عرض رسالة سريعة"""
        try:
            Snackbar(text=message, duration=2).open()
        except Exception:
            print(f"Message: {message}")

    def show_success_dialog(self, title, message):
        """عرض حوار النجاح"""
        try:
            def close_dialog(obj):
                dialog.dismiss()

            dialog = MDDialog(
                title=title,
                text=message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=close_dialog
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            print(f"Success: {title} - {message}")

    def show_error_dialog(self, title, message):
        """عرض حوار الخطأ"""
        try:
            def close_dialog(obj):
                dialog.dismiss()

            dialog = MDDialog(
                title=title,
                text=message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=close_dialog
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            print(f"Error: {title} - {message}")

    def restart_app(self):
        """إعادة تشغيل التطبيق"""
        try:
            self.stop()
            self.__init__()
            self.run()
        except Exception as e:
            print(f"Restart error: {e}")

# دالة تشغيل التطبيق
def run_mobile_app():
    """تشغيل التطبيق"""
    if not KIVY_AVAILABLE:
        print("❌ Error: Kivy not available")
        print("💡 Install with: pip install kivy kivymd")
        return False

    try:
        print("🚀 Starting Twitter Video Downloader Pro...")
        app = TwitterDownloaderMobileApp()
        app.run()
        return True
    except Exception as e:
        print(f"❌ Error running app: {e}")
        return False

# تشغيل التطبيق إذا تم تشغيل الملف مباشرة
if __name__ == "__main__":
    run_mobile_app()
