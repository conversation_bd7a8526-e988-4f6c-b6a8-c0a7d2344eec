#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تحميل الفيديوهات من تويتر
Twitter Video Downloader
"""

import os
import re
import yt_dlp
import config

class TwitterVideoDownloader:
    def __init__(self):
        self.download_path = config.DEFAULT_DOWNLOAD_PATH
        self.quality = "best"
        self.progress_callback = None
        
        # إنشاء مجلد التحميل إذا لم يكن موجوداً
        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path)
    
    def set_download_path(self, path):
        """تحديد مسار التحميل"""
        self.download_path = path
        if not os.path.exists(path):
            os.makedirs(path)
    
    def set_quality(self, quality):
        """تحديد جودة الفيديو"""
        self.quality = quality
    
    def set_progress_callback(self, callback):
        """تحديد دالة لمتابعة التقدم"""
        self.progress_callback = callback
    
    def is_valid_twitter_url(self, url):
        """التحقق من صحة رابط تويتر"""
        twitter_patterns = [
            r'https?://(?:www\.)?twitter\.com/\w+/status/\d+',
            r'https?://(?:www\.)?x\.com/\w+/status/\d+',
            r'https?://t\.co/\w+'
        ]
        
        for pattern in twitter_patterns:
            if re.match(pattern, url):
                return True
        return False
    
    def progress_hook(self, d):
        """دالة لمتابعة تقدم التحميل"""
        if self.progress_callback:
            if d['status'] == 'downloading':
                try:
                    percent = d.get('_percent_str', 'N/A')
                    speed = d.get('_speed_str', 'N/A')
                    eta = d.get('_eta_str', 'N/A')
                    self.progress_callback(f"التقدم: {percent} | السرعة: {speed} | الوقت المتبقي: {eta}")
                except:
                    self.progress_callback("جاري التحميل...")
            elif d['status'] == 'finished':
                self.progress_callback("تم التحميل بنجاح!")
    
    def download_video(self, url):
        """تحميل الفيديو من الرابط"""
        try:
            if not self.is_valid_twitter_url(url):
                return False, "رابط تويتر غير صحيح"
            
            # إعداد خيارات yt-dlp
            ydl_opts = {
                'format': self.quality,
                'outtmpl': os.path.join(self.download_path, '%(uploader)s_%(title)s.%(ext)s'),
                'progress_hooks': [self.progress_hook],
                'ignoreerrors': False,
                'no_warnings': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # الحصول على معلومات الفيديو
                info = ydl.extract_info(url, download=False)
                title = info.get('title', 'Unknown')
                duration = info.get('duration', 0)
                uploader = info.get('uploader', 'Unknown')
                
                if self.progress_callback:
                    self.progress_callback(f"العنوان: {title}")
                    self.progress_callback(f"المدة: {duration} ثانية")
                    self.progress_callback(f"الناشر: {uploader}")
                
                # تحميل الفيديو
                ydl.download([url])
                
                return True, "تم التحميل بنجاح!"
                
        except Exception as e:
            error_msg = f"خطأ في التحميل: {str(e)}"
            return False, error_msg
    
    def get_video_info(self, url):
        """الحصول على معلومات الفيديو بدون تحميل"""
        try:
            if not self.is_valid_twitter_url(url):
                return None, "رابط تويتر غير صحيح"
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                video_info = {
                    'title': info.get('title', 'غير معروف'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'غير معروف'),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'description': info.get('description', ''),
                    'upload_date': info.get('upload_date', ''),
                    'formats': len(info.get('formats', []))
                }
                
                return video_info, None
                
        except Exception as e:
            return None, f"خطأ في الحصول على المعلومات: {str(e)}"

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("برنامج تحميل فيديوهات تويتر - سطر الأوامر")
    print("Twitter Video Downloader - Command Line Interface")
    print("=" * 60)

    downloader = TwitterVideoDownloader()

    def progress_callback(message):
        print(f"📊 {message}")

    downloader.set_progress_callback(progress_callback)

    while True:
        print("\n" + "=" * 40)
        print("الخيارات المتاحة:")
        print("1. تحميل فيديو")
        print("2. معاينة معلومات الفيديو")
        print("3. تغيير مجلد التحميل")
        print("4. تغيير جودة الفيديو")
        print("5. خروج")
        print("=" * 40)

        choice = input("اختر رقم الخيار: ").strip()

        if choice == "1":
            url = input("\n📎 أدخل رابط الفيديو من تويتر: ").strip()
            if url:
                print("🚀 جاري التحميل...")
                success, message = downloader.download_video(url)
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
            else:
                print("❌ لم يتم إدخال رابط صحيح")

        elif choice == "2":
            url = input("\n📎 أدخل رابط الفيديو للمعاينة: ").strip()
            if url:
                print("🔍 جاري الحصول على المعلومات...")
                info, error = downloader.get_video_info(url)
                if error:
                    print(f"❌ {error}")
                else:
                    print("\n📋 معلومات الفيديو:")
                    print(f"📝 العنوان: {info['title']}")
                    print(f"👤 الناشر: {info['uploader']}")
                    print(f"⏱️ المدة: {info['duration']} ثانية")
                    print(f"👀 المشاهدات: {info['view_count']}")
                    print(f"❤️ الإعجابات: {info['like_count']}")
            else:
                print("❌ لم يتم إدخال رابط صحيح")

        elif choice == "3":
            current_path = downloader.download_path
            print(f"\n📂 المجلد الحالي: {current_path}")
            new_path = input("📁 أدخل المجلد الجديد (اتركه فارغ للإبقاء على الحالي): ").strip()
            if new_path:
                downloader.set_download_path(new_path)
                print(f"✅ تم تغيير مجلد التحميل إلى: {new_path}")

        elif choice == "4":
            print("\n🎬 خيارات الجودة:")
            qualities = list(config.VIDEO_QUALITY_OPTIONS.keys())
            for i, quality in enumerate(qualities, 1):
                print(f"{i}. {quality}")

            quality_choice = input("اختر رقم الجودة: ").strip()
            try:
                quality_index = int(quality_choice) - 1
                if 0 <= quality_index < len(qualities):
                    selected_quality = qualities[quality_index]
                    quality_value = config.VIDEO_QUALITY_OPTIONS[selected_quality]
                    downloader.set_quality(quality_value)
                    print(f"✅ تم تغيير الجودة إلى: {selected_quality}")
                else:
                    print("❌ رقم غير صحيح")
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح")

        elif choice == "5":
            print("👋 شكراً لاستخدام البرنامج!")
            break

        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")

        input("\n⏸️ اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
