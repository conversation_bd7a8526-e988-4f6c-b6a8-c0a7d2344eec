@echo off
chcp 65001 >nul
title برنامج تحميل فيديوهات تويتر

echo ========================================
echo برنامج تحميل فيديوهات تويتر
echo Twitter Video Downloader
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات المطلوبة
echo جاري التحقق من المكتبات المطلوبة...
pip show yt-dlp >nul 2>&1
if errorlevel 1 (
    echo تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo فشل في تثبيت المكتبات المطلوبة
        pause
        exit /b 1
    )
)

echo تشغيل البرنامج...
echo.
python main.py

pause
