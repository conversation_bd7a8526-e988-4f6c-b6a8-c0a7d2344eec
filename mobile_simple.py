#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Twitter Video Downloader - Simple Mobile Version
نسخة بسيطة للاختبار بدون أخطاء
"""

import os
import sys
import threading
import subprocess
import json
import re
from pathlib import Path

# التحقق من توفر المكتبات
try:
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton, MDFlatButton
    from kivymd.uix.textfield import MDTextField
    from kivymd.uix.label import MDLabel
    from kivymd.uix.card import MDCard
    from kivymd.uix.toolbar import MDTopAppBar
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.snackbar import Snackbar
    from kivymd.uix.progressbar import MDProgressBar
    from kivy.uix.scrollview import ScrollView
    from kivy.metrics import dp
    from kivy.core.window import Window
    from kivy.clock import Clock
    
    print("✅ All libraries loaded successfully")
    
except ImportError as e:
    print(f"❌ Error loading libraries: {e}")
    print("💡 Install with: pip install kivy kivymd")
    sys.exit(1)

class SimpleDownloader:
    """محرك تحميل بسيط باستخدام yt-dlp"""

    def __init__(self):
        self.download_path = self.get_download_path()
        self.progress_callback = None
        self.quality = "best"

    def set_progress_callback(self, callback):
        """تعيين دالة تحديث التقدم"""
        self.progress_callback = callback

    def set_quality(self, quality):
        """تعيين جودة التحميل"""
        self.quality = quality

    def get_download_path(self):
        """الحصول على مسار التحميل"""
        try:
            # للأندرويد
            from android.storage import primary_external_storage_path
            download_path = os.path.join(primary_external_storage_path(), "Download", "TwitterVideos")
        except ImportError:
            # للكمبيوتر
            download_path = os.path.join(os.path.expanduser("~"), "Downloads", "TwitterVideos")

        os.makedirs(download_path, exist_ok=True)
        return download_path

    def get_video_info(self, url):
        """الحصول على معلومات الفيديو"""
        try:
            # التحقق من صحة الرابط
            if not self.is_valid_twitter_url(url):
                return None, "Invalid Twitter URL"

            # محاولة استخدام yt-dlp
            cmd = [
                "yt-dlp",
                "--dump-json",
                "--no-download",
                url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                info = json.loads(result.stdout)
                return {
                    'title': info.get('title', 'Unknown'),
                    'uploader': info.get('uploader', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'upload_date': info.get('upload_date', 'Unknown'),
                    'description': info.get('description', '')
                }, None
            else:
                return None, "Could not get video information"

        except subprocess.TimeoutExpired:
            return None, "Request timeout"
        except FileNotFoundError:
            return None, "yt-dlp not found. Please install: pip install yt-dlp"
        except Exception as e:
            return None, str(e)

    def download_video(self, url):
        """تحميل الفيديو"""
        try:
            if not self.is_valid_twitter_url(url):
                return False, "Invalid Twitter URL"

            # إعداد الأوامر
            output_template = os.path.join(self.download_path, "%(title)s.%(ext)s")

            cmd = [
                "yt-dlp",
                "-f", self.quality,
                "-o", output_template,
                url
            ]

            if self.progress_callback:
                self.progress_callback("Starting download...")

            # تشغيل التحميل
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            stdout, stderr = process.communicate()

            if process.returncode == 0:
                if self.progress_callback:
                    self.progress_callback("Download completed!")
                return True, f"Video downloaded to: {self.download_path}"
            else:
                error_msg = stderr or "Unknown error"
                return False, f"Download failed: {error_msg}"

        except FileNotFoundError:
            return False, "yt-dlp not found. Please install: pip install yt-dlp"
        except Exception as e:
            return False, str(e)

    def is_valid_twitter_url(self, url):
        """التحقق من صحة رابط تويتر"""
        twitter_patterns = [
            r'https?://(www\.)?(twitter|x)\.com/.+/status/\d+',
            r'https?://t\.co/.+',
            r'https?://mobile\.twitter\.com/.+/status/\d+'
        ]

        for pattern in twitter_patterns:
            if re.match(pattern, url):
                return True
        return False

class SimpleTwitterDownloader(MDApp):
    """تطبيق بسيط لتحميل فيديوهات تويتر"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Twitter Video Downloader - Simple"
        
        # إعداد المظهر
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Orange"
        
        # متغيرات التطبيق
        self.current_quality = "Best Quality"
        self.quality_options = ["Best Quality", "1080p", "720p", "480p"]
        self.quality_index = 0
        self.is_downloading = False

        # إنشاء محرك التحميل
        self.downloader = SimpleDownloader()
        self.downloader.set_progress_callback(self.update_progress)

        print("✅ App initialized")

    def build(self):
        """بناء الواجهة"""
        try:
            print("🔨 Building interface...")
            
            # تعيين حجم النافذة
            Window.size = (400, 600)
            
            # إنشاء الواجهة
            return self.create_interface()
            
        except Exception as e:
            print(f"❌ Error building interface: {e}")
            return MDLabel(text=f"Error: {e}", halign="center")
    
    def create_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الحاوية الرئيسية
        root = MDBoxLayout(orientation='vertical')
        
        # شريط العنوان
        toolbar = MDTopAppBar(
            title="Twitter Video Downloader",
            elevation=2
        )
        root.add_widget(toolbar)
        
        # منطقة المحتوى
        scroll = ScrollView()
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=dp(20),
            adaptive_height=True
        )
        
        # بطاقة الترحيب
        welcome_card = self.create_welcome_card()
        content.add_widget(welcome_card)
        
        # بطاقة إدخال الرابط
        url_card = self.create_url_card()
        content.add_widget(url_card)
        
        # بطاقة الإعدادات
        settings_card = self.create_settings_card()
        content.add_widget(settings_card)
        
        # بطاقة المعلومات
        info_card = self.create_info_card()
        content.add_widget(info_card)
        
        # بطاقة التحميل
        download_card = self.create_download_card()
        content.add_widget(download_card)
        
        scroll.add_widget(content)
        root.add_widget(scroll)
        
        print("✅ Interface created successfully")
        return root
    
    def create_welcome_card(self):
        """بطاقة الترحيب"""
        card = MDCard(
            orientation='vertical',
            padding=dp(20),
            spacing=dp(10),
            elevation=3,
            size_hint_y=None,
            height=dp(100),
            md_bg_color=self.theme_cls.primary_color
        )
        
        title = MDLabel(
            text="Twitter Video Downloader",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="H5",
            bold=True,
            halign="center"
        )
        
        subtitle = MDLabel(
            text="Simple and Easy to Use",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 0.8),
            font_style="Body1",
            halign="center"
        )
        
        card.add_widget(title)
        card.add_widget(subtitle)
        
        return card
    
    def create_url_card(self):
        """بطاقة إدخال الرابط"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(120)
        )
        
        title = MDLabel(
            text="Video URL",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)
        
        self.url_input = MDTextField(
            hint_text="Paste Twitter video URL here...",
            helper_text="Supports twitter.com, x.com, t.co",
            helper_text_mode="on_focus",
            multiline=False
        )
        card.add_widget(self.url_input)
        
        buttons = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(10),
            size_hint_y=None,
            height=dp(40)
        )
        
        paste_btn = MDRaisedButton(
            text="Paste",
            size_hint_x=None,
            width=dp(80),
            on_release=self.paste_url
        )
        
        clear_btn = MDRaisedButton(
            text="Clear",
            size_hint_x=None,
            width=dp(80),
            on_release=self.clear_url
        )
        
        preview_btn = MDRaisedButton(
            text="Preview",
            on_release=self.preview_video
        )
        
        buttons.add_widget(paste_btn)
        buttons.add_widget(clear_btn)
        buttons.add_widget(preview_btn)
        
        card.add_widget(buttons)
        return card
    
    def create_settings_card(self):
        """بطاقة الإعدادات"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(80)
        )
        
        title = MDLabel(
            text="Settings",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)
        
        settings = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(10)
        )
        
        quality_label = MDLabel(
            text="Quality:",
            size_hint_x=None,
            width=dp(60)
        )
        
        self.quality_btn = MDRaisedButton(
            text=self.current_quality,
            on_release=self.change_quality
        )
        
        settings.add_widget(quality_label)
        settings.add_widget(self.quality_btn)
        
        card.add_widget(settings)
        return card
    
    def create_info_card(self):
        """بطاقة المعلومات"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(100)
        )
        
        title = MDLabel(
            text="Video Information",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)
        
        self.info_label = MDLabel(
            text="No information available",
            theme_text_color="Secondary"
        )
        card.add_widget(self.info_label)
        
        return card
    
    def create_download_card(self):
        """بطاقة التحميل"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(100)
        )
        
        title = MDLabel(
            text="Download",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        card.add_widget(title)
        
        self.progress_bar = MDProgressBar()
        card.add_widget(self.progress_bar)
        
        self.status_label = MDLabel(
            text="Ready to download",
            theme_text_color="Secondary",
            halign="center"
        )
        card.add_widget(self.status_label)
        
        self.download_btn = MDRaisedButton(
            text="Download",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.start_download
        )
        card.add_widget(self.download_btn)
        
        return card
    
    # الدوال الوظيفية
    def paste_url(self, instance):
        """لصق الرابط"""
        try:
            from kivy.core.clipboard import Clipboard
            content = Clipboard.paste()
            if content:
                self.url_input.text = content.strip()
                self.show_message("URL pasted")
            else:
                self.show_message("Clipboard is empty")
        except Exception as e:
            self.show_message(f"Paste error: {e}")
    
    def clear_url(self, instance):
        """مسح الرابط"""
        self.url_input.text = ""
        self.info_label.text = "No information available"
        self.show_message("URL cleared")
    
    def change_quality(self, instance):
        """تغيير الجودة"""
        self.quality_index = (self.quality_index + 1) % len(self.quality_options)
        self.current_quality = self.quality_options[self.quality_index]
        self.quality_btn.text = self.current_quality
        self.show_message(f"Quality: {self.current_quality}")
    
    def preview_video(self, instance):
        """معاينة الفيديو"""
        url = self.url_input.text.strip()
        if not url:
            self.show_message("Please enter a URL")
            return

        self.status_label.text = "Getting video info..."
        self.progress_bar.start()

        # تشغيل في خيط منفصل
        threading.Thread(target=self._preview_thread, args=(url,), daemon=True).start()

    def _preview_thread(self, url):
        """خيط معاينة الفيديو"""
        try:
            info, error = self.downloader.get_video_info(url)
            Clock.schedule_once(lambda dt: self._update_preview_ui(info, error), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_preview_ui(None, str(e)), 0)

    def _update_preview_ui(self, info, error):
        """تحديث واجهة المعاينة"""
        self.progress_bar.stop()

        if error:
            self.status_label.text = f"Error: {error}"
            self.info_label.text = f"❌ Error: {error}"
            self.show_message(f"Error: {error}")
        else:
            self.status_label.text = "Information retrieved"
            info_text = f"""📝 Title: {info['title']}
👤 Uploader: {info['uploader']}
⏱️ Duration: {info['duration']} seconds
👀 Views: {info['view_count']:,}
❤️ Likes: {info['like_count']:,}
📅 Upload Date: {info['upload_date']}

📄 Description:
{info['description'][:100] if info['description'] else 'No description'}..."""

            self.info_label.text = info_text
            self.show_message("Video info retrieved successfully")

    def start_download(self, instance):
        """بدء التحميل"""
        url = self.url_input.text.strip()
        if not url:
            self.show_message("Please enter a URL")
            return

        if self.is_downloading:
            self.show_message("Already downloading")
            return

        self.is_downloading = True
        self.download_btn.disabled = True
        self.status_label.text = "Starting download..."
        self.progress_bar.start()

        # إعداد الجودة
        quality_map = {
            "Best Quality": "best",
            "1080p": "best[height<=1080]",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]"
        }

        quality = quality_map.get(self.current_quality, "best")
        self.downloader.set_quality(quality)

        # تشغيل التحميل في خيط منفصل
        threading.Thread(target=self._download_thread, args=(url,), daemon=True).start()

    def _download_thread(self, url):
        """خيط التحميل"""
        try:
            success, message = self.downloader.download_video(url)
            Clock.schedule_once(lambda dt: self._update_download_ui(success, message), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_download_ui(False, str(e)), 0)

    def _update_download_ui(self, success, message):
        """تحديث واجهة التحميل"""
        self.progress_bar.stop()
        self.download_btn.disabled = False
        self.is_downloading = False

        if success:
            self.status_label.text = "✅ Download completed!"
            self.show_success(f"Download completed!\n{message}")
        else:
            self.status_label.text = "❌ Download failed"
            self.show_message(f"Download failed: {message}")

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', message), 0)
    
    def show_message(self, message):
        """عرض رسالة"""
        try:
            Snackbar(text=message, duration=2).open()
        except:
            print(f"Message: {message}")
    
    def show_success(self, message):
        """عرض رسالة نجاح"""
        try:
            dialog = MDDialog(
                title="Success",
                text=message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
        except:
            print(f"Success: {message}")

def main():
    """الدالة الرئيسية"""
    print("🚀 Starting Simple Twitter Video Downloader...")
    
    try:
        app = SimpleTwitterDownloader()
        app.run()
        print("✅ App closed successfully")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
