# إعدادات البرنامج
import os

# مجلد التحميل الافتراضي
DEFAULT_DOWNLOAD_PATH = os.path.join(os.path.expanduser("~"), "Downloads", "Twitter_Videos")

# إعدادات جودة الفيديو
VIDEO_QUALITY_OPTIONS = {
    "أفضل جودة متاحة": "best",
    "1080p": "best[height<=1080]",
    "720p": "best[height<=720]",
    "480p": "best[height<=480]",
    "360p": "best[height<=360]"
}

# إعدادات yt-dlp
YT_DLP_OPTIONS = {
    'format': 'best',
    'outtmpl': os.path.join(DEFAULT_DOWNLOAD_PATH, '%(title)s.%(ext)s'),
    'writesubtitles': False,
    'writeautomaticsub': False,
    'ignoreerrors': True,
    'no_warnings': False,
    'extractaudio': False,
    'audioformat': 'mp3',
    'embed_subs': False,
    'writeinfojson': False,
    'writethumbnail': False
}

# رسائل الحالة
STATUS_MESSAGES = {
    'downloading': 'جاري التحميل...',
    'completed': 'تم التحميل بنجاح!',
    'error': 'حدث خطأ أثناء التحميل',
    'invalid_url': 'رابط غير صحيح',
    'ready': 'جاهز للتحميل'
}
