@echo off
chcp 65001 >nul
title برنامج تحميل فيديوهات تويتر - الواجهة الرسومية

echo ========================================
echo 🎬 برنامج تحميل فيديوهات تويتر
echo Twitter Video Downloader - GUI
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات المطلوبة
echo 🔍 جاري التحقق من المكتبات المطلوبة...
pip show yt-dlp >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات المطلوبة
        pause
        exit /b 1
    )
)

echo 🚀 تشغيل الواجهة الرسومية...
echo.
python gui.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل الواجهة الرسومية
    echo 💡 جرب تشغيل سطر الأوامر بدلاً من ذلك
    echo.
    set /p choice="هل تريد تشغيل سطر الأوامر؟ (y/n): "
    if /i "%choice%"=="y" (
        python twitter_downloader.py
    )
)

pause
