@echo off
chcp 65001 >nul
title 🚀 بناء APK باستخدام WSL

cls
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █         🚀 بناء APK باستخدام WSL                            █
echo █            Building APK using WSL                            █
echo █                                                              █
echo █              Twitter Video Downloader Pro                   █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🔍 جاري فحص WSL...
echo.

REM التحقق من وجود WSL
wsl --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: WSL غير مثبت
    echo.
    echo 💡 لتثبيت WSL:
    echo 1. افتح PowerShell كمدير
    echo 2. شغل: wsl --install Ubuntu
    echo 3. أعد تشغيل الكمبيوتر
    echo 4. أكمل إعداد Ubuntu
    echo.
    pause
    exit /b 1
)
echo ✅ WSL متاح

echo.
echo 📁 إنشاء مجلد المشروع في WSL...
wsl mkdir -p ~/twitter-downloader-apk

echo 📋 نسخ ملفات المشروع...
wsl cp "/mnt/c/Users/<USER>/Desktop/mobile_simple.py" ~/twitter-downloader-apk/ 2>nul
if errorlevel 1 (
    echo ⚠️ تحذير: لم يتم العثور على mobile_simple.py في سطح المكتب
    echo 💡 تأكد من وجود الملفات في المسار الصحيح
)

wsl cp "/mnt/c/Users/<USER>/Desktop/main.py" ~/twitter-downloader-apk/ 2>nul
wsl cp "/mnt/c/Users/<USER>/Desktop/buildozer.spec" ~/twitter-downloader-apk/ 2>nul

echo.
echo 🔧 تثبيت المتطلبات في WSL...
echo ⏳ هذا قد يستغرق بضع دقائق...
echo.

wsl bash -c "cd ~/twitter-downloader-apk && sudo apt update && sudo apt install -y python3 python3-pip openjdk-8-jdk autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev"

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo 💡 جرب تشغيل الأوامر يدوياً في WSL
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت buildozer...
wsl bash -c "cd ~/twitter-downloader-apk && pip3 install --user buildozer cython"

if errorlevel 1 (
    echo ❌ فشل في تثبيت buildozer
    pause
    exit /b 1
)

echo.
echo 🏗️ بدء بناء APK...
echo ⏳ هذا قد يستغرق 30-60 دقيقة في المرة الأولى
echo 💡 لا تغلق النافذة حتى اكتمال البناء
echo.

wsl bash -c "cd ~/twitter-downloader-apk && export PATH=$PATH:~/.local/bin && buildozer android debug"

if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء APK
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. تأكد من وجود مساحة كافية (2-3 GB)
    echo 3. جرب: wsl bash -c "cd ~/twitter-downloader-apk && buildozer android clean"
    echo 4. ثم أعد تشغيل هذا الملف
    echo.
) else (
    echo.
    echo ✅ تم بناء APK بنجاح!
    echo.
    echo 📁 نسخ APK إلى سطح المكتب...
    wsl cp ~/twitter-downloader-apk/bin/*.apk "/mnt/c/Users/<USER>/Desktop/"
    
    if errorlevel 1 (
        echo ⚠️ لم يتم نسخ APK تلقائياً
        echo 💡 يمكنك العثور على APK في WSL في المسار:
        echo    ~/twitter-downloader-apk/bin/
    ) else (
        echo ✅ تم نسخ APK إلى سطح المكتب!
    )
    
    echo.
    echo 🎉 اسم الملف: twitterdownloaderpro-2.1-arm64-v8a-debug.apk
    echo 📱 يمكنك الآن تثبيته على هاتف الأندرويد
    echo.
    
    REM فتح سطح المكتب
    start "" "%USERPROFILE%\Desktop"
)

echo.
echo 👋 انتهى بناء APK
pause
