
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق موبايل لتحميل فيديوهات تويتر
Twitter Video Downloader Mobile App
"""

import os
import sys
import threading
import json
from pathlib import Path

# استيراد Kivy
try:
    from kivy.app import App
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.gridlayout import GridLayout
    from kivy.uix.label import Label
    from kivy.uix.textinput import TextInput
    from kivy.uix.button import Button
    from kivy.uix.progressbar import ProgressBar
    from kivy.uix.spinner import Spinner
    from kivy.uix.switch import Switch
    from kivy.uix.popup import Popup
    from kivy.uix.scrollview import ScrollView
    from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
    from kivy.clock import Clock
    from kivy.metrics import dp
    from kivy.core.window import Window
    
    # KivyMD للمظهر الجميل
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton, MDIconButton
    from kivymd.uix.textfield import MDTextField
    from kivymd.uix.label import MDLabel
    from kivymd.uix.card import MDCard
    from kivymd.uix.toolbar import MDTopAppBar
    from kivymd.uix.navigationdrawer import MDNavigationDrawer
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.screenmanager import MDScreenManager
    from kivymd.uix.tab import MDTabs, MDTabsBase
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.snackbar import Snackbar
    from kivymd.uix.progressbar import MDProgressBar
    from kivymd.uix.selectioncontrol import MDSwitch
    from kivymd.uix.menu import MDDropdownMenu
    
    KIVY_AVAILABLE = True
except ImportError as e:
    KIVY_AVAILABLE = False
    print(f"❌ خطأ: Kivy غير متاح - {e}")
    print("💡 لتثبيت Kivy: pip install kivy kivymd")

# استيراد محرك التحميل
try:
    from twitter_downloader import TwitterVideoDownloader
    import config
except ImportError:
    print("❌ خطأ: لم يتم العثور على ملفات البرنامج الأساسية")
    sys.exit(1)

class TwitterDownloaderMobile(MDApp):
    """تطبيق موبايل لتحميل فيديوهات تويتر"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Twitter Video Downloader"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "DeepPurple"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.material_style = "M3"
        
        # متغيرات التطبيق
        self.downloader = None
        self.is_downloading = False
        self.download_thread = None
        self.settings = self.load_settings()
        
        # عناصر الواجهة
        self.url_field = None
        self.quality_menu = None
        self.progress_bar = None
        self.status_label = None
        self.download_btn = None
        self.info_label = None
        
    def build(self):
        """بناء واجهة التطبيق"""
        try:
            # إنشاء محرك التحميل
            self.downloader = TwitterVideoDownloader()
            self.downloader.set_progress_callback(self.update_progress)
            
            # تعيين حجم النافذة للاختبار على الكمبيوتر
            Window.size = (400, 700)
            
            # إنشاء الشاشة الرئيسية
            return self.create_main_screen()
            
        except Exception as e:
            self.show_error(f"خطأ في تهيئة التطبيق: {e}")
            return MDLabel(text="خطأ في التطبيق", halign="center")
    
    def create_main_screen(self):
        """إنشاء الشاشة الرئيسية المحسنة"""
        # الحاوية الرئيسية مع تمرير
        from kivymd.uix.scrollview import MDScrollView

        scroll = MDScrollView()
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=dp(20),
            adaptive_height=True
        )

        # شريط العنوان المحسن
        toolbar = MDTopAppBar(
            title="Twitter Video Downloader",
            elevation=3,
            left_action_items=[["menu", lambda x: self.open_settings()]],
            right_action_items=[
                ["help-circle", lambda x: self.show_help()],
                ["theme-light-dark", lambda x: self.toggle_theme()]
            ]
        )

        # إضافة عنوان ترحيبي
        welcome_card = self.create_welcome_card()
        main_layout.add_widget(welcome_card)

        # بطاقة إدخال الرابط المحسنة
        url_card = self.create_url_input_card()
        main_layout.add_widget(url_card)

        # بطاقة الإعدادات السريعة المحسنة
        settings_card = self.create_quick_settings_card()
        main_layout.add_widget(settings_card)

        # بطاقة معلومات الفيديو المحسنة
        info_card = self.create_info_card()
        main_layout.add_widget(info_card)

        # بطاقة التحميل المحسنة
        download_card = self.create_download_card()
        main_layout.add_widget(download_card)

        # بطاقة الإحصائيات
        stats_card = self.create_stats_card()
        main_layout.add_widget(stats_card)

        # إضافة التخطيط إلى التمرير
        scroll = MDScrollView()
        scroll.add_widget(main_layout)

        # إضافة الشريط والمحتوى للتخطيط الجذر
        root_layout = MDBoxLayout(orientation='vertical')
        root_layout.add_widget(toolbar)
        root_layout.add_widget(scroll)

        return root_layout

    def create_welcome_card(self):
        """إنشاء بطاقة الترحيب"""
        card = MDCard(
            orientation='vertical',
            padding=dp(20),
            spacing=dp(10),
            elevation=4,
            size_hint_y=None,
            height=dp(120),
            md_bg_color=self.theme_cls.primary_color,
            radius=[15, 15, 15, 15]
        )

        # أيقونة وعنوان
        header_layout = MDBoxLayout(orientation='horizontal', spacing=dp(15))

        # أيقونة التطبيق
        from kivymd.uix.label import MDIcon
        icon = MDIcon(
            icon="video-box",
            theme_icon_color="Custom",
            icon_color=[1, 1, 1, 1],
            icon_size=dp(40)
        )
        header_layout.add_widget(icon)

        # النصوص
        text_layout = MDBoxLayout(orientation='vertical')

        title = MDLabel(
            text="Twitter Video Downloader",
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            font_style="H5",
            bold=True,
            size_hint_y=None,
            height=dp(35)
        )
        text_layout.add_widget(title)

        subtitle = MDLabel(
            text="حمل فيديوهاتك المفضلة بجودة عالية",
            theme_text_color="Custom",
            text_color=[1, 1, 1, 0.8],
            font_style="Body1",
            size_hint_y=None,
            height=dp(25)
        )
        text_layout.add_widget(subtitle)

        header_layout.add_widget(text_layout)
        card.add_widget(header_layout)

        return card
    
    def create_url_input_card(self):
        """إنشاء بطاقة إدخال الرابط المحسنة"""
        card = MDCard(
            orientation='vertical',
            padding=dp(20),
            spacing=dp(15),
            elevation=3,
            size_hint_y=None,
            height=dp(160),
            radius=[10, 10, 10, 10]
        )

        # عنوان البطاقة مع أيقونة
        header_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(35))

        from kivymd.uix.label import MDIcon
        icon = MDIcon(
            icon="link-variant",
            theme_icon_color="Primary",
            icon_size=dp(24)
        )
        header_layout.add_widget(icon)

        title = MDLabel(
            text="رابط الفيديو",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        header_layout.add_widget(title)

        card.add_widget(header_layout)

        # حقل إدخال الرابط المحسن
        self.url_field = MDTextField(
            hint_text="Paste Twitter video URL here...",
            helper_text="Supports twitter.com, x.com, t.co",
            helper_text_mode="on_focus",
            multiline=False,
            size_hint_y=None,
            height=dp(60),
            mode="outlined",
            line_color_focus=self.theme_cls.primary_color
        )
        card.add_widget(self.url_field)

        # أزرار سريعة محسنة
        buttons_layout = MDBoxLayout(orientation='horizontal', spacing=dp(15), size_hint_y=None, height=dp(45))

        # زر لصق محسن
        paste_btn = MDRaisedButton(
            text="Paste",
            icon="content-paste",
            size_hint_x=None,
            width=dp(80),
            md_bg_color=self.theme_cls.accent_color,
            on_release=self.paste_from_clipboard
        )
        buttons_layout.add_widget(paste_btn)

        # زر مسح محسن
        clear_btn = MDRaisedButton(
            text="Clear",
            icon="delete-outline",
            size_hint_x=None,
            width=dp(80),
            md_bg_color=self.theme_cls.error_color,
            on_release=self.clear_url
        )
        buttons_layout.add_widget(clear_btn)

        # زر معاينة محسن
        preview_btn = MDRaisedButton(
            text="Preview",
            icon="eye-outline",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.preview_video
        )
        buttons_layout.add_widget(preview_btn)

        card.add_widget(buttons_layout)

        return card

    def create_quick_settings_card(self):
        """إنشاء بطاقة الإعدادات السريعة"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(100)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="Quick Settings",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # إعدادات في صف واحد
        settings_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10))

        # اختيار الجودة
        quality_label = MDLabel(text="Quality:", size_hint_x=None, width=dp(80))
        settings_layout.add_widget(quality_label)

        # قائمة الجودة - مبسطة
        self.quality_btn = MDRaisedButton(
            text="Best Quality",
            size_hint_x=None,
            width=dp(120),
            on_release=self.cycle_quality
        )
        settings_layout.add_widget(self.quality_btn)

        # قائمة الجودات المتاحة
        self.quality_options = ["Best Quality", "1080p", "720p", "480p"]
        self.current_quality_index = 0

        card.add_widget(settings_layout)

        return card

    def create_info_card(self):
        """إنشاء بطاقة معلومات الفيديو"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(200)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="Video Information",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # منطقة المعلومات مع تمرير
        scroll = ScrollView()
        self.info_label = MDLabel(
            text="No information available",
            theme_text_color="Secondary",
            text_size=(None, None),
            halign="left"
        )
        scroll.add_widget(self.info_label)
        card.add_widget(scroll)

        return card

    def create_download_card(self):
        """إنشاء بطاقة التحميل"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(150)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="Download",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # شريط التقدم
        self.progress_bar = MDProgressBar(
            size_hint_y=None,
            height=dp(10)
        )
        card.add_widget(self.progress_bar)

        # رسالة الحالة
        self.status_label = MDLabel(
            text="Ready to download",
            theme_text_color="Secondary",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        card.add_widget(self.status_label)

        # أزرار التحكم
        buttons_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(50))

        self.download_btn = MDRaisedButton(
            text="Download",
            icon="download",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.start_download
        )
        buttons_layout.add_widget(self.download_btn)

        self.stop_btn = MDRaisedButton(
            text="Stop",
            icon="stop",
            md_bg_color=self.theme_cls.error_color,
            disabled=True,
            on_release=self.stop_download
        )
        buttons_layout.add_widget(self.stop_btn)

        card.add_widget(buttons_layout)

        return card

    def paste_from_clipboard(self, *args):
        """لصق من الحافظة"""
        try:
            # محاولة الحصول على محتوى الحافظة
            from kivy.core.clipboard import Clipboard
            clipboard_content = Clipboard.paste()

            if clipboard_content and clipboard_content.startswith(('http://', 'https://')):
                self.url_field.text = clipboard_content.strip()
                self.show_snackbar("URL pasted from clipboard")
            else:
                self.show_snackbar("No valid URL in clipboard")
        except Exception as e:
            self.show_snackbar(f"Paste error: {e}")

    def clear_url(self, *args):
        """مسح الرابط"""
        self.url_field.text = ""
        self.info_label.text = "No information available"
        self.show_snackbar("URL cleared")

    def cycle_quality(self, *args):
        """تبديل الجودة بالتتابع"""
        self.current_quality_index = (self.current_quality_index + 1) % len(self.quality_options)
        selected_quality = self.quality_options[self.current_quality_index]
        self.quality_btn.text = selected_quality

        # تحديث الإعدادات
        quality_map = {
            "Best Quality": "best",
            "1080p": "best[height<=1080]",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]"
        }

        self.settings['quality'] = quality_map.get(selected_quality, "best")
        self.show_snackbar(f"Quality: {selected_quality}")

    def preview_video(self, *args):
        """معاينة معلومات الفيديو"""
        url = self.url_field.text.strip()
        if not url:
            self.show_snackbar("Please enter video URL")
            return

        self.status_label.text = "Getting video information..."
        self.progress_bar.start()

        # تشغيل في خيط منفصل
        threading.Thread(target=self._preview_thread, args=(url,), daemon=True).start()

    def _preview_thread(self, url):
        """خيط معاينة الفيديو"""
        try:
            info, error = self.downloader.get_video_info(url)

            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_preview_ui(info, error), 0)

        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_preview_ui(None, str(e)), 0)

    def _update_preview_ui(self, info, error):
        """تحديث واجهة المعاينة"""
        self.progress_bar.stop()

        if error:
            self.status_label.text = f"Error: {error}"
            self.info_label.text = f"❌ Error: {error}"
        else:
            self.status_label.text = "Information retrieved"
            info_text = f"""📝 Title: {info['title']}
👤 Uploader: {info['uploader']}
⏱️ Duration: {info['duration']} seconds
👀 Views: {info['view_count']:,}
❤️ Likes: {info['like_count']:,}
📅 Upload Date: {info['upload_date']}

📄 Description:
{info['description'][:200] if info['description'] else 'No description'}..."""

            self.info_label.text = info_text
            self.info_label.text_size = (dp(300), None)

    def start_download(self, *args):
        """بدء التحميل"""
        url = self.url_field.text.strip()
        if not url:
            self.show_snackbar("Please enter video URL")
            return

        if self.is_downloading:
            self.show_snackbar("Already downloading a video")
            return

        # تحديث الواجهة
        self.download_btn.disabled = True
        self.stop_btn.disabled = False
        self.is_downloading = True
        self.progress_bar.start()
        self.status_label.text = "Downloading..."

        # تحديث إعدادات المحرك
        quality_map = {
            "أفضل جودة": "best",
            "1080p": "best[height<=1080]",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]"
        }

        quality = quality_map.get(self.quality_btn.text, "best")
        self.downloader.set_quality(quality)

        # تحديد مسار التحميل (مجلد التحميلات في الهاتف)
        download_path = self.get_download_path()
        self.downloader.set_download_path(download_path)

        # بدء التحميل في خيط منفصل
        self.download_thread = threading.Thread(target=self._download_thread, args=(url,), daemon=True)
        self.download_thread.start()

    def _download_thread(self, url):
        """خيط التحميل"""
        try:
            success, message = self.downloader.download_video(url)

            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_download_ui(success, message), 0)

        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_download_ui(False, str(e)), 0)

    def _update_download_ui(self, success, message):
        """تحديث واجهة التحميل"""
        self.progress_bar.stop()
        self.download_btn.disabled = False
        self.stop_btn.disabled = True
        self.is_downloading = False

        if success:
            self.status_label.text = "✅ Download completed!"
            self.show_success_dialog("Download Successful!", message)
        else:
            self.status_label.text = "❌ Download failed"
            self.show_error_dialog("Download Failed", message)

    def stop_download(self, *args):
        """إيقاف التحميل"""
        self.is_downloading = False
        self.download_btn.disabled = False
        self.stop_btn.disabled = True
        self.progress_bar.stop()
        self.status_label.text = "Download stopped"
        self.show_snackbar("Download stopped")

    def get_download_path(self):
        """الحصول على مسار التحميل المناسب للموبايل"""
        try:
            # محاولة استخدام مجلد التحميلات في الأندرويد
            from android.storage import primary_external_storage_path
            download_path = os.path.join(primary_external_storage_path(), "Download", "TwitterVideos")
        except ImportError:
            # للاختبار على الكمبيوتر
            download_path = os.path.join(os.path.expanduser("~"), "Downloads", "TwitterVideos")

        # إنشاء المجلد إذا لم يكن موجود
        os.makedirs(download_path, exist_ok=True)
        return download_path

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', message), 0)

    def show_snackbar(self, message):
        """عرض رسالة سريعة"""
        Snackbar(text=message, duration=2).open()

    def show_success_dialog(self, title, message):
        """عرض حوار النجاح"""
        from kivymd.uix.button import MDFlatButton

        def close_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title=title,
            buttons=[
                MDFlatButton(
                    text="موافق",
                    on_release=close_dialog
                )
            ]
        )
        # إضافة النص كمحتوى
        dialog.text = message
        dialog.open()

    def show_error_dialog(self, title, message):
        """عرض حوار الخطأ"""
        from kivymd.uix.button import MDFlatButton

        def close_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title=title,
            buttons=[
                MDFlatButton(
                    text="موافق",
                    on_release=close_dialog
                )
            ]
        )
        # إضافة النص كمحتوى
        dialog.text = message
        dialog.open()

    def show_error(self, message):
        """عرض خطأ"""
        self.show_error_dialog("خطأ", message)

    def open_settings(self):
        """فتح الإعدادات"""
        self.show_snackbar("الإعدادات قريباً...")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """🎬 تطبيق تحميل فيديوهات تويتر

📖 كيفية الاستخدام:
1. الصق رابط الفيديو من تويتر
2. اختر جودة الفيديو
3. اضغط معاينة لرؤية المعلومات
4. اضغط تحميل

🔗 الروابط المدعومة:
• twitter.com
• x.com
• t.co

💡 نصائح:
• تأكد من اتصال الإنترنت
• اختر جودة أقل للتحميل السريع
• الفيديوهات تُحفظ في مجلد التحميلات"""

        from kivymd.uix.button import MDFlatButton

        def close_help_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title="❓ المساعدة",
            buttons=[
                MDFlatButton(
                    text="فهمت",
                    on_release=close_help_dialog
                )
            ]
        )
        dialog.text = help_text
        dialog.open()

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), 'mobile_settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass

        # الإعدادات الافتراضية
        return {
            'quality': 'best',
            'auto_download': False,
            'notifications': True
        }

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), 'mobile_settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def create_stats_card(self):
        """إنشاء بطاقة الإحصائيات"""
        card = MDCard(
            orientation='vertical',
            padding=dp(20),
            spacing=dp(15),
            elevation=2,
            size_hint_y=None,
            height=dp(120),
            radius=[10, 10, 10, 10]
        )

        # عنوان البطاقة
        header_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(35))

        from kivymd.uix.label import MDIcon
        icon = MDIcon(
            icon="chart-line",
            theme_icon_color="Primary",
            icon_size=dp(24)
        )
        header_layout.add_widget(icon)

        title = MDLabel(
            text="App Statistics",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        header_layout.add_widget(title)

        card.add_widget(header_layout)

        # الإحصائيات
        stats_layout = MDBoxLayout(orientation='horizontal', spacing=dp(20))

        # عدد التحميلات
        downloads_layout = MDBoxLayout(orientation='vertical', size_hint_x=None, width=dp(100))
        downloads_count = MDLabel(
            text="0",
            theme_text_color="Primary",
            font_style="H4",
            bold=True,
            halign="center"
        )
        downloads_layout.add_widget(downloads_count)

        downloads_label = MDLabel(
            text="Downloads",
            theme_text_color="Secondary",
            font_style="Caption",
            halign="center"
        )
        downloads_layout.add_widget(downloads_label)

        stats_layout.add_widget(downloads_layout)

        # حجم البيانات
        data_layout = MDBoxLayout(orientation='vertical', size_hint_x=None, width=dp(100))
        data_size = MDLabel(
            text="0 MB",
            theme_text_color="Primary",
            font_style="H4",
            bold=True,
            halign="center"
        )
        data_layout.add_widget(data_size)

        data_label = MDLabel(
            text="Data Size",
            theme_text_color="Secondary",
            font_style="Caption",
            halign="center"
        )
        data_layout.add_widget(data_label)

        stats_layout.add_widget(data_layout)

        # وقت التوفير
        time_layout = MDBoxLayout(orientation='vertical')
        time_saved = MDLabel(
            text="0 min",
            theme_text_color="Primary",
            font_style="H4",
            bold=True,
            halign="center"
        )
        time_layout.add_widget(time_saved)

        time_label = MDLabel(
            text="Time Saved",
            theme_text_color="Secondary",
            font_style="Caption",
            halign="center"
        )
        time_layout.add_widget(time_label)

        stats_layout.add_widget(time_layout)

        card.add_widget(stats_layout)

        return card

    def toggle_theme(self):
        """تبديل المظهر بين الفاتح والداكن"""
        if self.theme_cls.theme_style == "Light":
            self.theme_cls.theme_style = "Dark"
            self.show_snackbar("Switched to Dark Theme")
        else:
            self.theme_cls.theme_style = "Light"
            self.show_snackbar("Switched to Light Theme")

    def on_stop(self):
        """عند إغلاق التطبيق"""
        self.save_settings()
        if self.is_downloading:
            self.is_downloading = False

# دالة تشغيل التطبيق
def run_mobile_app():
    """تشغيل التطبيق"""
    if not KIVY_AVAILABLE:
        print("❌ خطأ: Kivy غير متاح")
        print("💡 لتثبيت Kivy:")
        print("pip install kivy kivymd")
        return False

    try:
        app = TwitterDownloaderMobile()
        app.run()
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

if __name__ == "__main__":
    run_mobile_app()



