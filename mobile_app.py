#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق موبايل لتحميل فيديوهات تويتر
Twitter Video Downloader Mobile App
"""

import os
import sys
import threading
import json
from pathlib import Path

# استيراد Kivy
try:
    from kivy.app import App
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.gridlayout import GridLayout
    from kivy.uix.label import Label
    from kivy.uix.textinput import TextInput
    from kivy.uix.button import Button
    from kivy.uix.progressbar import ProgressBar
    from kivy.uix.spinner import Spinner
    from kivy.uix.switch import Switch
    from kivy.uix.popup import Popup
    from kivy.uix.scrollview import ScrollView
    from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
    from kivy.clock import Clock
    from kivy.metrics import dp
    from kivy.core.window import Window
    
    # KivyMD للمظهر الجميل
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton, MDIconButton
    from kivymd.uix.textfield import MDTextField
    from kivymd.uix.label import MDLabel
    from kivymd.uix.card import MDCard
    from kivymd.uix.toolbar import MDTopAppBar
    from kivymd.uix.navigationdrawer import MDNavigationDrawer
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.screenmanager import MDScreenManager
    from kivymd.uix.tab import MDTabs, MDTabsBase
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.snackbar import Snackbar
    from kivymd.uix.progressbar import MDProgressBar
    from kivymd.uix.selectioncontrol import MDSwitch
    from kivymd.uix.menu import MDDropdownMenu
    
    KIVY_AVAILABLE = True
except ImportError as e:
    KIVY_AVAILABLE = False
    print(f"❌ خطأ: Kivy غير متاح - {e}")
    print("💡 لتثبيت Kivy: pip install kivy kivymd")

# استيراد محرك التحميل
try:
    from twitter_downloader import TwitterVideoDownloader
    import config
except ImportError:
    print("❌ خطأ: لم يتم العثور على ملفات البرنامج الأساسية")
    sys.exit(1)

class TwitterDownloaderMobile(MDApp):
    """تطبيق موبايل لتحميل فيديوهات تويتر"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "🎬 تحميل فيديوهات تويتر"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Orange"
        
        # متغيرات التطبيق
        self.downloader = None
        self.is_downloading = False
        self.download_thread = None
        self.settings = self.load_settings()
        
        # عناصر الواجهة
        self.url_field = None
        self.quality_menu = None
        self.progress_bar = None
        self.status_label = None
        self.download_btn = None
        self.info_label = None
        
    def build(self):
        """بناء واجهة التطبيق"""
        try:
            # إنشاء محرك التحميل
            self.downloader = TwitterVideoDownloader()
            self.downloader.set_progress_callback(self.update_progress)
            
            # تعيين حجم النافذة للاختبار على الكمبيوتر
            Window.size = (400, 700)
            
            # إنشاء الشاشة الرئيسية
            return self.create_main_screen()
            
        except Exception as e:
            self.show_error(f"خطأ في تهيئة التطبيق: {e}")
            return MDLabel(text="خطأ في التطبيق", halign="center")
    
    def create_main_screen(self):
        """إنشاء الشاشة الرئيسية"""
        # الحاوية الرئيسية
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))
        
        # شريط العنوان
        toolbar = MDTopAppBar(
            title="🎬 تحميل فيديوهات تويتر",
            elevation=2,
            left_action_items=[["menu", lambda x: self.open_settings()]],
            right_action_items=[["information", lambda x: self.show_help()]]
        )
        main_layout.add_widget(toolbar)
        
        # بطاقة إدخال الرابط
        url_card = self.create_url_input_card()
        main_layout.add_widget(url_card)
        
        # بطاقة الإعدادات السريعة
        settings_card = self.create_quick_settings_card()
        main_layout.add_widget(settings_card)
        
        # بطاقة معلومات الفيديو
        info_card = self.create_info_card()
        main_layout.add_widget(info_card)
        
        # بطاقة التحميل
        download_card = self.create_download_card()
        main_layout.add_widget(download_card)
        
        return main_layout
    
    def create_url_input_card(self):
        """إنشاء بطاقة إدخال الرابط"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(120)
        )
        
        # عنوان البطاقة
        title = MDLabel(
            text="📎 رابط الفيديو",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)
        
        # حقل إدخال الرابط
        self.url_field = MDTextField(
            hint_text="الصق رابط الفيديو من تويتر هنا...",
            helper_text="يدعم twitter.com و x.com و t.co",
            helper_text_mode="on_focus",
            multiline=False,
            size_hint_y=None,
            height=dp(60)
        )
        card.add_widget(self.url_field)
        
        # أزرار سريعة
        buttons_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(40))
        
        paste_btn = MDIconButton(
            icon="content-paste",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color,
            on_release=self.paste_from_clipboard
        )
        buttons_layout.add_widget(paste_btn)
        
        clear_btn = MDIconButton(
            icon="close",
            theme_icon_color="Custom", 
            icon_color=self.theme_cls.error_color,
            on_release=self.clear_url
        )
        buttons_layout.add_widget(clear_btn)
        
        preview_btn = MDRaisedButton(
            text="👁️ معاينة",
            size_hint_x=None,
            width=dp(100),
            on_release=self.preview_video
        )
        buttons_layout.add_widget(preview_btn)
        
        card.add_widget(buttons_layout)
        
        return card

    def create_quick_settings_card(self):
        """إنشاء بطاقة الإعدادات السريعة"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(100)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="⚙️ إعدادات سريعة",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # إعدادات في صف واحد
        settings_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10))

        # اختيار الجودة
        quality_label = MDLabel(text="🎬 الجودة:", size_hint_x=None, width=dp(80))
        settings_layout.add_widget(quality_label)

        # قائمة الجودة
        quality_items = [
            {"text": "أفضل جودة", "viewclass": "OneLineListItem", "on_release": lambda x="best": self.set_quality(x)},
            {"text": "1080p", "viewclass": "OneLineListItem", "on_release": lambda x="1080p": self.set_quality(x)},
            {"text": "720p", "viewclass": "OneLineListItem", "on_release": lambda x="720p": self.set_quality(x)},
            {"text": "480p", "viewclass": "OneLineListItem", "on_release": lambda x="480p": self.set_quality(x)},
        ]

        self.quality_btn = MDRaisedButton(
            text="أفضل جودة",
            size_hint_x=None,
            width=dp(120),
            on_release=self.open_quality_menu
        )
        settings_layout.add_widget(self.quality_btn)

        self.quality_menu = MDDropdownMenu(
            caller=self.quality_btn,
            items=quality_items,
            width_mult=4,
        )

        card.add_widget(settings_layout)

        return card

    def create_info_card(self):
        """إنشاء بطاقة معلومات الفيديو"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(200)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="📋 معلومات الفيديو",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # منطقة المعلومات مع تمرير
        scroll = ScrollView()
        self.info_label = MDLabel(
            text="لا توجد معلومات متاحة",
            theme_text_color="Secondary",
            text_size=(None, None),
            halign="right"
        )
        scroll.add_widget(self.info_label)
        card.add_widget(scroll)

        return card

    def create_download_card(self):
        """إنشاء بطاقة التحميل"""
        card = MDCard(
            orientation='vertical',
            padding=dp(15),
            spacing=dp(10),
            elevation=2,
            size_hint_y=None,
            height=dp(150)
        )

        # عنوان البطاقة
        title = MDLabel(
            text="📥 التحميل",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title)

        # شريط التقدم
        self.progress_bar = MDProgressBar(
            size_hint_y=None,
            height=dp(10)
        )
        card.add_widget(self.progress_bar)

        # رسالة الحالة
        self.status_label = MDLabel(
            text="جاهز للتحميل",
            theme_text_color="Secondary",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        card.add_widget(self.status_label)

        # أزرار التحكم
        buttons_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(50))

        self.download_btn = MDRaisedButton(
            text="⬇️ تحميل",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.start_download
        )
        buttons_layout.add_widget(self.download_btn)

        self.stop_btn = MDRaisedButton(
            text="⏹️ إيقاف",
            md_bg_color=self.theme_cls.error_color,
            disabled=True,
            on_release=self.stop_download
        )
        buttons_layout.add_widget(self.stop_btn)

        card.add_widget(buttons_layout)

        return card

    def paste_from_clipboard(self, *args):
        """لصق من الحافظة"""
        try:
            # محاولة الحصول على محتوى الحافظة
            from kivy.core.clipboard import Clipboard
            clipboard_content = Clipboard.paste()

            if clipboard_content and clipboard_content.startswith(('http://', 'https://')):
                self.url_field.text = clipboard_content.strip()
                self.show_snackbar("تم لصق الرابط من الحافظة")
            else:
                self.show_snackbar("لا يوجد رابط صحيح في الحافظة")
        except Exception as e:
            self.show_snackbar(f"خطأ في اللصق: {e}")

    def clear_url(self, *args):
        """مسح الرابط"""
        self.url_field.text = ""
        self.info_label.text = "لا توجد معلومات متاحة"
        self.show_snackbar("تم مسح الرابط")

    def open_quality_menu(self, *args):
        """فتح قائمة الجودة"""
        self.quality_menu.open()

    def set_quality(self, quality):
        """تحديد جودة الفيديو"""
        quality_map = {
            "best": "أفضل جودة",
            "1080p": "1080p",
            "720p": "720p",
            "480p": "480p"
        }

        self.quality_btn.text = quality_map.get(quality, "أفضل جودة")
        self.settings['quality'] = quality
        self.quality_menu.dismiss()
        self.show_snackbar(f"تم تحديد الجودة: {quality_map.get(quality)}")

    def preview_video(self, *args):
        """معاينة معلومات الفيديو"""
        url = self.url_field.text.strip()
        if not url:
            self.show_snackbar("يرجى إدخال رابط الفيديو")
            return

        self.status_label.text = "جاري الحصول على المعلومات..."
        self.progress_bar.start()

        # تشغيل في خيط منفصل
        threading.Thread(target=self._preview_thread, args=(url,), daemon=True).start()

    def _preview_thread(self, url):
        """خيط معاينة الفيديو"""
        try:
            info, error = self.downloader.get_video_info(url)

            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_preview_ui(info, error), 0)

        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_preview_ui(None, str(e)), 0)

    def _update_preview_ui(self, info, error):
        """تحديث واجهة المعاينة"""
        self.progress_bar.stop()

        if error:
            self.status_label.text = f"خطأ: {error}"
            self.info_label.text = f"❌ خطأ: {error}"
        else:
            self.status_label.text = "تم الحصول على المعلومات"
            info_text = f"""📝 العنوان: {info['title']}
👤 الناشر: {info['uploader']}
⏱️ المدة: {info['duration']} ثانية
👀 المشاهدات: {info['view_count']:,}
❤️ الإعجابات: {info['like_count']:,}
📅 تاريخ النشر: {info['upload_date']}

📄 الوصف:
{info['description'][:200] if info['description'] else 'لا يوجد وصف'}..."""

            self.info_label.text = info_text
            self.info_label.text_size = (dp(300), None)

    def start_download(self, *args):
        """بدء التحميل"""
        url = self.url_field.text.strip()
        if not url:
            self.show_snackbar("يرجى إدخال رابط الفيديو")
            return

        if self.is_downloading:
            self.show_snackbar("يتم تحميل فيديو بالفعل")
            return

        # تحديث الواجهة
        self.download_btn.disabled = True
        self.stop_btn.disabled = False
        self.is_downloading = True
        self.progress_bar.start()
        self.status_label.text = "جاري التحميل..."

        # تحديث إعدادات المحرك
        quality_map = {
            "أفضل جودة": "best",
            "1080p": "best[height<=1080]",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]"
        }

        quality = quality_map.get(self.quality_btn.text, "best")
        self.downloader.set_quality(quality)

        # تحديد مسار التحميل (مجلد التحميلات في الهاتف)
        download_path = self.get_download_path()
        self.downloader.set_download_path(download_path)

        # بدء التحميل في خيط منفصل
        self.download_thread = threading.Thread(target=self._download_thread, args=(url,), daemon=True)
        self.download_thread.start()

    def _download_thread(self, url):
        """خيط التحميل"""
        try:
            success, message = self.downloader.download_video(url)

            # تحديث الواجهة في الخيط الرئيسي
            Clock.schedule_once(lambda dt: self._update_download_ui(success, message), 0)

        except Exception as e:
            Clock.schedule_once(lambda dt: self._update_download_ui(False, str(e)), 0)

    def _update_download_ui(self, success, message):
        """تحديث واجهة التحميل"""
        self.progress_bar.stop()
        self.download_btn.disabled = False
        self.stop_btn.disabled = True
        self.is_downloading = False

        if success:
            self.status_label.text = "✅ تم التحميل بنجاح!"
            self.show_success_dialog("تم التحميل بنجاح!", message)
        else:
            self.status_label.text = f"❌ فشل التحميل"
            self.show_error_dialog("فشل التحميل", message)

    def stop_download(self, *args):
        """إيقاف التحميل"""
        self.is_downloading = False
        self.download_btn.disabled = False
        self.stop_btn.disabled = True
        self.progress_bar.stop()
        self.status_label.text = "تم إيقاف التحميل"
        self.show_snackbar("تم إيقاف التحميل")

    def get_download_path(self):
        """الحصول على مسار التحميل المناسب للموبايل"""
        try:
            # محاولة استخدام مجلد التحميلات في الأندرويد
            from android.storage import primary_external_storage_path
            download_path = os.path.join(primary_external_storage_path(), "Download", "TwitterVideos")
        except ImportError:
            # للاختبار على الكمبيوتر
            download_path = os.path.join(os.path.expanduser("~"), "Downloads", "TwitterVideos")

        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(download_path, exist_ok=True)
        return download_path

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', message), 0)

    def show_snackbar(self, message):
        """عرض رسالة سريعة"""
        Snackbar(text=message, duration=2).open()

    def show_success_dialog(self, title, message):
        """عرض حوار النجاح"""
        from kivymd.uix.button import MDFlatButton

        def close_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title=title,
            buttons=[
                MDFlatButton(
                    text="موافق",
                    on_release=close_dialog
                )
            ]
        )
        # إضافة النص كمحتوى
        dialog.text = message
        dialog.open()

    def show_error_dialog(self, title, message):
        """عرض حوار الخطأ"""
        from kivymd.uix.button import MDFlatButton

        def close_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title=title,
            buttons=[
                MDFlatButton(
                    text="موافق",
                    on_release=close_dialog
                )
            ]
        )
        # إضافة النص كمحتوى
        dialog.text = message
        dialog.open()

    def show_error(self, message):
        """عرض خطأ"""
        self.show_error_dialog("خطأ", message)

    def open_settings(self):
        """فتح الإعدادات"""
        self.show_snackbar("الإعدادات قريباً...")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """🎬 تطبيق تحميل فيديوهات تويتر

📖 كيفية الاستخدام:
1. الصق رابط الفيديو من تويتر
2. اختر جودة الفيديو
3. اضغط معاينة لرؤية المعلومات
4. اضغط تحميل

🔗 الروابط المدعومة:
• twitter.com
• x.com
• t.co

💡 نصائح:
• تأكد من اتصال الإنترنت
• اختر جودة أقل للتحميل السريع
• الفيديوهات تُحفظ في مجلد التحميلات"""

        from kivymd.uix.button import MDFlatButton

        def close_help_dialog(obj):
            dialog.dismiss()

        dialog = MDDialog(
            title="❓ المساعدة",
            buttons=[
                MDFlatButton(
                    text="فهمت",
                    on_release=close_help_dialog
                )
            ]
        )
        dialog.text = help_text
        dialog.open()

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), 'mobile_settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass

        # الإعدادات الافتراضية
        return {
            'quality': 'best',
            'auto_download': False,
            'notifications': True
        }

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), 'mobile_settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def on_stop(self):
        """عند إغلاق التطبيق"""
        self.save_settings()
        if self.is_downloading:
            self.is_downloading = False

# دالة تشغيل التطبيق
def run_mobile_app():
    """تشغيل التطبيق"""
    if not KIVY_AVAILABLE:
        print("❌ خطأ: Kivy غير متاح")
        print("💡 لتثبيت Kivy:")
        print("pip install kivy kivymd")
        return False

    try:
        app = TwitterDownloaderMobile()
        app.run()
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

if __name__ == "__main__":
    run_mobile_app()
