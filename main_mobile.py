#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقطة دخول التطبيق الموبايل
Mobile App Entry Point
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(__file__))

try:
    # محاولة تشغيل التطبيق الموبايل
    from mobile_app import run_mobile_app
    
    if __name__ == "__main__":
        print("🚀 تشغيل تطبيق تحميل فيديوهات تويتر...")
        success = run_mobile_app()
        
        if not success:
            print("❌ فشل في تشغيل التطبيق الموبايل")
            print("💡 تأكد من تثبيت المتطلبات:")
            print("pip install -r requirements_mobile.txt")
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    sys.exit(1)
