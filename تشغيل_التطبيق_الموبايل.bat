@echo off
chcp 65001 >nul
title 📱 تطبيق تحميل فيديوهات تويتر - موبايل

cls
echo.
echo ████████████████████████████████████████████████
echo █                                              █
echo █     📱 تطبيق تحميل فيديوهات تويتر - موبايل     █
echo █        Twitter Video Downloader Mobile       █
echo █                                              █
echo ████████████████████████████████████████████████
echo.

echo 🔍 جاري التحقق من المتطلبات...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)
echo ✅ Python متاح

REM التحقق من وجود Kivy
python -c "import kivy" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت Kivy و KivyMD...
    pip install kivy kivymd
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Kivy
        echo 💡 جرب: pip install --upgrade pip
        pause
        exit /b 1
    )
)
echo ✅ Kivy متاح

REM التحقق من باقي المتطلبات
echo 📦 تثبيت المتطلبات الإضافية...
pip install -r requirements_mobile.txt >nul 2>&1

echo.
echo 🚀 تشغيل التطبيق الموبايل...
echo 💡 سيفتح في نافذة محاكي الموبايل
echo.

python mobile_app.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo 💡 تحقق من الأخطاء أعلاه
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. pip install --upgrade kivy kivymd
    echo 2. pip install --upgrade yt-dlp
    echo 3. python -m pip install --upgrade pip
    echo.
)

echo.
echo 👋 شكراً لاستخدام التطبيق!
pause
