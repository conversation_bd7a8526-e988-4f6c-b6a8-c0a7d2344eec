#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرسومية لبرنامج تحميل فيديوهات تويتر
Twitter Video Downloader GUI
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from twitter_downloader import TwitterVideoDownloader
import config

class TwitterDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج تحميل فيديوهات تويتر")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # إنشاء كائن التحميل
        self.downloader = TwitterVideoDownloader()
        self.downloader.set_progress_callback(self.update_progress)
        
        # متغيرات الواجهة
        self.url_var = tk.StringVar()
        self.quality_var = tk.StringVar(value="أفضل جودة متاحة")
        self.download_path_var = tk.StringVar(value=config.DEFAULT_DOWNLOAD_PATH)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # عنوان البرنامج
        title_label = ttk.Label(main_frame, text="برنامج تحميل فيديوهات تويتر", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # إدخال الرابط
        ttk.Label(main_frame, text="رابط الفيديو:").grid(row=1, column=0, sticky=tk.W, pady=5)
        url_entry = ttk.Entry(main_frame, textvariable=self.url_var, width=50)
        url_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # زر معاينة المعلومات
        preview_btn = ttk.Button(main_frame, text="معاينة", command=self.preview_video)
        preview_btn.grid(row=1, column=2, pady=5, padx=(5, 0))
        
        # اختيار الجودة
        ttk.Label(main_frame, text="جودة الفيديو:").grid(row=2, column=0, sticky=tk.W, pady=5)
        quality_combo = ttk.Combobox(main_frame, textvariable=self.quality_var, 
                                   values=list(config.VIDEO_QUALITY_OPTIONS.keys()),
                                   state="readonly")
        quality_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # مسار التحميل
        ttk.Label(main_frame, text="مجلد التحميل:").grid(row=3, column=0, sticky=tk.W, pady=5)
        path_entry = ttk.Entry(main_frame, textvariable=self.download_path_var, width=50)
        path_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # زر اختيار المجلد
        browse_btn = ttk.Button(main_frame, text="تصفح", command=self.browse_folder)
        browse_btn.grid(row=3, column=2, pady=5, padx=(5, 0))
        
        # معلومات الفيديو
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الفيديو", padding="5")
        info_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        info_frame.columnconfigure(0, weight=1)
        
        self.info_text = scrolledtext.ScrolledText(info_frame, height=6, width=70)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # شريط التقدم
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="جاهز للتحميل")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=20)
        
        self.download_btn = ttk.Button(button_frame, text="تحميل الفيديو", 
                                     command=self.start_download, style="Accent.TButton")
        self.download_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = ttk.Button(button_frame, text="مسح", command=self.clear_fields)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        open_folder_btn = ttk.Button(button_frame, text="فتح مجلد التحميل", 
                                   command=self.open_download_folder)
        open_folder_btn.pack(side=tk.LEFT, padx=5)
        
    def browse_folder(self):
        """اختيار مجلد التحميل"""
        folder = filedialog.askdirectory(initialdir=self.download_path_var.get())
        if folder:
            self.download_path_var.set(folder)
            self.downloader.set_download_path(folder)
    
    def clear_fields(self):
        """مسح الحقول"""
        self.url_var.set("")
        self.info_text.delete(1.0, tk.END)
        self.progress_var.set("جاهز للتحميل")
        self.progress_bar.stop()
    
    def open_download_folder(self):
        """فتح مجلد التحميل"""
        path = self.download_path_var.get()
        if os.path.exists(path):
            os.startfile(path)  # Windows
        else:
            messagebox.showwarning("تحذير", "مجلد التحميل غير موجود")
    
    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        self.progress_var.set(message)
        self.root.update_idletasks()
    
    def preview_video(self):
        """معاينة معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return
        
        self.info_text.delete(1.0, tk.END)
        self.progress_var.set("جاري الحصول على المعلومات...")
        self.progress_bar.start()
        
        def preview_thread():
            info, error = self.downloader.get_video_info(url)
            
            def update_ui():
                self.progress_bar.stop()
                if error:
                    self.progress_var.set(f"خطأ: {error}")
                    self.info_text.insert(tk.END, f"خطأ: {error}")
                else:
                    self.progress_var.set("تم الحصول على المعلومات")
                    info_text = f"""العنوان: {info['title']}
الناشر: {info['uploader']}
المدة: {info['duration']} ثانية
عدد المشاهدات: {info['view_count']}
عدد الإعجابات: {info['like_count']}
تاريخ النشر: {info['upload_date']}
عدد الصيغ المتاحة: {info['formats']}

الوصف:
{info['description'][:200]}..."""
                    self.info_text.insert(tk.END, info_text)
            
            self.root.after(0, update_ui)
        
        threading.Thread(target=preview_thread, daemon=True).start()
    
    def start_download(self):
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return
        
        # تحديث إعدادات التحميل
        quality = config.VIDEO_QUALITY_OPTIONS[self.quality_var.get()]
        self.downloader.set_quality(quality)
        self.downloader.set_download_path(self.download_path_var.get())
        
        # تعطيل زر التحميل
        self.download_btn.config(state="disabled")
        self.progress_bar.start()
        
        def download_thread():
            success, message = self.downloader.download_video(url)
            
            def update_ui():
                self.progress_bar.stop()
                self.download_btn.config(state="normal")
                
                if success:
                    messagebox.showinfo("نجح", message)
                    self.progress_var.set("تم التحميل بنجاح!")
                else:
                    messagebox.showerror("خطأ", message)
                    self.progress_var.set(f"خطأ: {message}")
            
            self.root.after(0, update_ui)
        
        threading.Thread(target=download_thread, daemon=True).start()

def main():
    """تشغيل البرنامج"""
    root = tk.Tk()
    app = TwitterDownloaderGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
