#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرسومية المحسنة لبرنامج تحميل فيديوهات تويتر
Enhanced Twitter Video Downloader GUI
"""

import sys
import os
import threading

# محاولة استيراد tkinter مع معالجة أفضل للأخطاء
try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    TKINTER_AVAILABLE = True
except ImportError as e:
    TKINTER_AVAILABLE = False
    print(f"❌ خطأ: tkinter غير متاح - {e}")
    print("💡 الحل: استخدم سطر الأوامر بدلاً من ذلك")

# استيراد المكتبات المطلوبة
try:
    from twitter_downloader import TwitterVideoDownloader
    import config
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    sys.exit(1)

class TwitterDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()

        # إنشاء كائن التحميل
        try:
            self.downloader = TwitterVideoDownloader()
            self.downloader.set_progress_callback(self.update_progress)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة محرك التحميل: {e}")
            return

        # متغيرات الواجهة
        self.url_var = tk.StringVar()
        self.quality_var = tk.StringVar(value="أفضل جودة متاحة")
        self.download_path_var = tk.StringVar(value=config.DEFAULT_DOWNLOAD_PATH)
        self.status_var = tk.StringVar(value="جاهز للتحميل")

        # متغيرات التحكم
        self.is_downloading = False
        self.download_thread = None

        self.setup_ui()
        self.setup_clipboard_monitor()

    def on_closing(self):
        """معالج إغلاق النافذة"""
        # إيقاف مراقبة الحافظة
        self.clipboard_monitor_active = False

        if self.is_downloading:
            if messagebox.askokcancel("تأكيد الخروج", "يتم تحميل فيديو حالياً. هل تريد الخروج؟"):
                if self.download_thread and self.download_thread.is_alive():
                    # إيقاف التحميل
                    self.is_downloading = False
                self.root.destroy()
        else:
            self.root.destroy()

    def setup_clipboard_monitor(self):
        """مراقبة الحافظة للروابط"""
        self.last_clipboard = ""
        self.clipboard_monitor_active = True
        self.monitor_clipboard()

    def monitor_clipboard(self):
        """مراقبة الحافظة بشكل دوري"""
        if not self.clipboard_monitor_active:
            return

        try:
            current_clipboard = self.root.clipboard_get()
            if (current_clipboard != self.last_clipboard and
                current_clipboard.startswith(('http://', 'https://')) and
                ('twitter.com' in current_clipboard or 'x.com' in current_clipboard or 't.co' in current_clipboard)):

                # عرض رسالة تأكيد
                if messagebox.askyesno("رابط جديد", f"تم اكتشاف رابط تويتر في الحافظة:\n\n{current_clipboard[:100]}...\n\nهل تريد استخدامه؟"):
                    self.url_var.set(current_clipboard)

                self.last_clipboard = current_clipboard
        except:
            pass

        # إعادة المراقبة كل ثانيتين إذا كانت النافذة ما زالت موجودة
        if self.clipboard_monitor_active:
            try:
                self.root.after(2000, self.monitor_clipboard)
            except:
                self.clipboard_monitor_active = False

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎬 برنامج تحميل فيديوهات تويتر")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # تعيين أيقونة النافذة (إذا كانت متاحة)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(600, 500)

        # معالج إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        # إنشاء النوت بوك للتبويبات
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب التحميل الرئيسي
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="📥 التحميل")

        # تبويب الإعدادات
        settings_tab = ttk.Frame(notebook)
        notebook.add(settings_tab, text="⚙️ الإعدادات")

        # تبويب المساعدة
        help_tab = ttk.Frame(notebook)
        notebook.add(help_tab, text="❓ المساعدة")

        # إعداد تبويب التحميل الرئيسي
        self.setup_main_tab(main_tab)

        # إعداد تبويب الإعدادات
        self.setup_settings_tab(settings_tab)

        # إعداد تبويب المساعدة
        self.setup_help_tab(help_tab)

    def setup_main_tab(self, parent):
        """إعداد تبويب التحميل الرئيسي"""
        # إطار رئيسي مع تمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان البرنامج مع أيقونة
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk.Label(title_frame, text="🎬 برنامج تحميل فيديوهات تويتر",
                               font=("Arial", 18, "bold"))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="حمل فيديوهاتك المفضلة بجودة عالية",
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(5, 0))

        # قسم إدخال الرابط
        url_frame = ttk.LabelFrame(main_frame, text="📎 رابط الفيديو", padding="15")
        url_frame.pack(fill=tk.X, pady=(0, 20))

        # إطار الرابط مع الأزرار
        url_input_frame = ttk.Frame(url_frame)
        url_input_frame.pack(fill=tk.X)

        # حقل إدخال الرابط
        self.url_entry = ttk.Entry(url_input_frame, textvariable=self.url_var,
                                  font=("Arial", 11), width=50)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # زر لصق من الحافظة
        paste_btn = ttk.Button(url_input_frame, text="📋 لصق",
                              command=self.paste_from_clipboard, width=8)
        paste_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر مسح الرابط
        clear_url_btn = ttk.Button(url_input_frame, text="🗑️ مسح",
                                  command=self.clear_url, width=8)
        clear_url_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر معاينة المعلومات
        preview_btn = ttk.Button(url_input_frame, text="👁️ معاينة",
                                command=self.preview_video, width=10)
        preview_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # نصائح للمستخدم
        tip_label = ttk.Label(url_frame,
                             text="💡 نصيحة: انسخ الرابط من تويتر وسيتم اكتشافه تلقائياً",
                             font=("Arial", 9), foreground="gray")
        tip_label.pack(pady=(10, 0))

        # قسم الإعدادات السريعة
        settings_frame = ttk.LabelFrame(main_frame, text="⚙️ إعدادات التحميل", padding="15")
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # إطار الجودة والمجلد
        settings_grid = ttk.Frame(settings_frame)
        settings_grid.pack(fill=tk.X)

        # اختيار الجودة
        ttk.Label(settings_grid, text="🎬 جودة الفيديو:").grid(row=0, column=0, sticky=tk.W, pady=5)
        quality_combo = ttk.Combobox(settings_grid, textvariable=self.quality_var,
                                   values=list(config.VIDEO_QUALITY_OPTIONS.keys()),
                                   state="readonly", width=20)
        quality_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 20), pady=5)

        # مسار التحميل
        ttk.Label(settings_grid, text="📁 مجلد التحميل:").grid(row=1, column=0, sticky=tk.W, pady=5)
        path_frame = ttk.Frame(settings_grid)
        path_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.path_entry = ttk.Entry(path_frame, textvariable=self.download_path_var,
                                   font=("Arial", 10), width=40)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        browse_btn = ttk.Button(path_frame, text="📂 تصفح", command=self.browse_folder, width=10)
        browse_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # تكوين الشبكة
        settings_grid.columnconfigure(2, weight=1)

        # قسم معلومات الفيديو
        info_frame = ttk.LabelFrame(main_frame, text="📋 معلومات الفيديو", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        self.info_text = scrolledtext.ScrolledText(info_frame, height=8, width=70,
                                                  font=("Arial", 10), wrap=tk.WORD)
        self.info_text.pack(fill=tk.BOTH, expand=True)

        # قسم التقدم والحالة
        progress_frame = ttk.LabelFrame(main_frame, text="📊 حالة التحميل", padding="15")
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        # شريط التقدم
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # رسالة الحالة
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                     font=("Arial", 10))
        self.status_label.pack()

        # قسم الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        # زر التحميل الرئيسي
        self.download_btn = ttk.Button(buttons_frame, text="⬇️ تحميل الفيديو",
                                     command=self.start_download,
                                     style="Accent.TButton")
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر إيقاف التحميل
        self.stop_btn = ttk.Button(buttons_frame, text="⏹️ إيقاف",
                                  command=self.stop_download, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر مسح الكل
        clear_all_btn = ttk.Button(buttons_frame, text="🗑️ مسح الكل",
                                  command=self.clear_all)
        clear_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر فتح مجلد التحميل
        open_folder_btn = ttk.Button(buttons_frame, text="📂 فتح المجلد",
                                    command=self.open_download_folder)
        open_folder_btn.pack(side=tk.RIGHT)

    def paste_from_clipboard(self):
        """لصق الرابط من الحافظة"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.url_var.set(clipboard_content.strip())
                self.status_var.set("تم لصق الرابط من الحافظة")
        except:
            messagebox.showwarning("تحذير", "لا يوجد محتوى في الحافظة")

    def clear_url(self):
        """مسح الرابط"""
        self.url_var.set("")
        self.status_var.set("تم مسح الرابط")

    def clear_all(self):
        """مسح جميع الحقول"""
        self.url_var.set("")
        self.info_text.delete(1.0, tk.END)
        self.status_var.set("تم مسح جميع البيانات")
        self.progress_bar.stop()

    def stop_download(self):
        """إيقاف التحميل"""
        self.is_downloading = False
        self.download_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()
        self.status_var.set("تم إيقاف التحميل")

    def setup_settings_tab(self, parent):
        """إعداد تبويب الإعدادات المتقدم"""
        # إطار رئيسي مع تمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        settings_frame = ttk.Frame(scrollable_frame, padding="20")
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان التبويب
        title_label = ttk.Label(settings_frame, text="⚙️ إعدادات البرنامج المتقدمة",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 30))

        # إعدادات التحميل
        self.setup_download_settings(settings_frame)

        # إعدادات الواجهة
        self.setup_interface_settings(settings_frame)

        # إعدادات متقدمة
        self.setup_advanced_settings(settings_frame)

        # إعدادات الشبكة
        self.setup_network_settings(settings_frame)

        # أزرار الإعدادات
        self.setup_settings_buttons(settings_frame)

    def setup_download_settings(self, parent):
        """إعداد إعدادات التحميل"""
        download_frame = ttk.LabelFrame(parent, text="📥 إعدادات التحميل", padding="15")
        download_frame.pack(fill=tk.X, pady=(0, 20))

        # متغيرات الإعدادات
        if not hasattr(self, 'auto_download_var'):
            self.auto_download_var = tk.BooleanVar(value=False)
            self.overwrite_var = tk.BooleanVar(value=False)
            self.create_subfolder_var = tk.BooleanVar(value=True)
            self.max_retries_var = tk.IntVar(value=3)
            self.timeout_var = tk.IntVar(value=30)

        # الصف الأول
        row1_frame = ttk.Frame(download_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        # تحميل تلقائي
        auto_check = ttk.Checkbutton(row1_frame, text="🔄 تحميل تلقائي عند اكتشاف رابط",
                                    variable=self.auto_download_var)
        auto_check.pack(side=tk.LEFT)

        # الكتابة فوق الملفات
        overwrite_check = ttk.Checkbutton(row1_frame, text="🔄 الكتابة فوق الملفات الموجودة",
                                         variable=self.overwrite_var)
        overwrite_check.pack(side=tk.RIGHT)

        # الصف الثاني
        row2_frame = ttk.Frame(download_frame)
        row2_frame.pack(fill=tk.X, pady=5)

        # إنشاء مجلد فرعي
        subfolder_check = ttk.Checkbutton(row2_frame, text="📁 إنشاء مجلد فرعي لكل ناشر",
                                         variable=self.create_subfolder_var)
        subfolder_check.pack(side=tk.LEFT)

        # الصف الثالث - إعدادات الشبكة
        row3_frame = ttk.Frame(download_frame)
        row3_frame.pack(fill=tk.X, pady=10)

        # عدد المحاولات
        ttk.Label(row3_frame, text="🔄 عدد المحاولات:").pack(side=tk.LEFT)
        retries_spin = ttk.Spinbox(row3_frame, from_=1, to=10, width=5,
                                  textvariable=self.max_retries_var)
        retries_spin.pack(side=tk.LEFT, padx=(5, 20))

        # مهلة الاتصال
        ttk.Label(row3_frame, text="⏱️ مهلة الاتصال (ثانية):").pack(side=tk.LEFT)
        timeout_spin = ttk.Spinbox(row3_frame, from_=10, to=120, width=5,
                                  textvariable=self.timeout_var)
        timeout_spin.pack(side=tk.LEFT, padx=(5, 0))

    def setup_interface_settings(self, parent):
        """إعداد إعدادات الواجهة"""
        interface_frame = ttk.LabelFrame(parent, text="🎨 إعدادات الواجهة", padding="15")
        interface_frame.pack(fill=tk.X, pady=(0, 20))

        # متغيرات الواجهة
        if not hasattr(self, 'theme_var'):
            self.theme_var = tk.StringVar(value="النظام الافتراضي")
            self.language_var = tk.StringVar(value="العربية")
            self.notifications_var = tk.BooleanVar(value=True)
            self.sound_var = tk.BooleanVar(value=True)
            self.minimize_to_tray_var = tk.BooleanVar(value=False)

        # الصف الأول
        row1_frame = ttk.Frame(interface_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        # اختيار المظهر
        ttk.Label(row1_frame, text="🎨 مظهر البرنامج:").pack(side=tk.LEFT)
        theme_combo = ttk.Combobox(row1_frame, textvariable=self.theme_var,
                                  values=["النظام الافتراضي", "فاتح", "داكن"],
                                  state="readonly", width=15)
        theme_combo.pack(side=tk.LEFT, padx=(10, 20))

        # اختيار اللغة
        ttk.Label(row1_frame, text="🌐 اللغة:").pack(side=tk.LEFT)
        lang_combo = ttk.Combobox(row1_frame, textvariable=self.language_var,
                                 values=["العربية", "English"],
                                 state="readonly", width=10)
        lang_combo.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الثاني
        row2_frame = ttk.Frame(interface_frame)
        row2_frame.pack(fill=tk.X, pady=10)

        # الإشعارات
        notifications_check = ttk.Checkbutton(row2_frame, text="🔔 إظهار الإشعارات",
                                            variable=self.notifications_var)
        notifications_check.pack(side=tk.LEFT)

        # الأصوات
        sound_check = ttk.Checkbutton(row2_frame, text="🔊 تشغيل الأصوات",
                                     variable=self.sound_var)
        sound_check.pack(side=tk.LEFT, padx=(20, 0))

        # تصغير إلى الشريط
        tray_check = ttk.Checkbutton(row2_frame, text="📌 تصغير إلى شريط المهام",
                                    variable=self.minimize_to_tray_var)
        tray_check.pack(side=tk.RIGHT)

    def setup_advanced_settings(self, parent):
        """إعداد الإعدادات المتقدمة"""
        advanced_frame = ttk.LabelFrame(parent, text="🔧 إعدادات متقدمة", padding="15")
        advanced_frame.pack(fill=tk.X, pady=(0, 20))

        # متغيرات متقدمة
        if not hasattr(self, 'download_subtitles_var'):
            self.download_subtitles_var = tk.BooleanVar(value=False)
            self.download_thumbnail_var = tk.BooleanVar(value=False)
            self.download_metadata_var = tk.BooleanVar(value=False)
            self.custom_filename_var = tk.StringVar(value="%(uploader)s_%(title)s.%(ext)s")
            self.concurrent_downloads_var = tk.IntVar(value=1)

        # الصف الأول - خيارات التحميل الإضافية
        row1_frame = ttk.Frame(advanced_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        subtitles_check = ttk.Checkbutton(row1_frame, text="📝 تحميل الترجمة",
                                         variable=self.download_subtitles_var)
        subtitles_check.pack(side=tk.LEFT)

        thumbnail_check = ttk.Checkbutton(row1_frame, text="🖼️ تحميل الصورة المصغرة",
                                         variable=self.download_thumbnail_var)
        thumbnail_check.pack(side=tk.LEFT, padx=(20, 0))

        metadata_check = ttk.Checkbutton(row1_frame, text="📊 حفظ البيانات الوصفية",
                                        variable=self.download_metadata_var)
        metadata_check.pack(side=tk.RIGHT)

        # الصف الثاني - نمط اسم الملف
        row2_frame = ttk.Frame(advanced_frame)
        row2_frame.pack(fill=tk.X, pady=10)

        ttk.Label(row2_frame, text="📄 نمط اسم الملف:").pack(side=tk.LEFT)
        filename_entry = ttk.Entry(row2_frame, textvariable=self.custom_filename_var,
                                  width=40, font=("Arial", 9))
        filename_entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        # زر إعادة تعيين نمط الاسم
        reset_btn = ttk.Button(row2_frame, text="🔄 إعادة تعيين",
                              command=self.reset_filename_pattern, width=12)
        reset_btn.pack(side=tk.RIGHT)

        # الصف الثالث - التحميلات المتزامنة
        row3_frame = ttk.Frame(advanced_frame)
        row3_frame.pack(fill=tk.X, pady=5)

        ttk.Label(row3_frame, text="⚡ عدد التحميلات المتزامنة:").pack(side=tk.LEFT)
        concurrent_spin = ttk.Spinbox(row3_frame, from_=1, to=5, width=5,
                                     textvariable=self.concurrent_downloads_var)
        concurrent_spin.pack(side=tk.LEFT, padx=(10, 20))

        # نصيحة
        tip_label = ttk.Label(row3_frame, text="💡 زيادة العدد قد تؤثر على الأداء",
                             font=("Arial", 8), foreground="gray")
        tip_label.pack(side=tk.LEFT)

    def setup_network_settings(self, parent):
        """إعداد إعدادات الشبكة"""
        network_frame = ttk.LabelFrame(parent, text="🌐 إعدادات الشبكة", padding="15")
        network_frame.pack(fill=tk.X, pady=(0, 20))

        # متغيرات الشبكة
        if not hasattr(self, 'use_proxy_var'):
            self.use_proxy_var = tk.BooleanVar(value=False)
            self.proxy_url_var = tk.StringVar(value="")
            self.user_agent_var = tk.StringVar(value="")
            self.rate_limit_var = tk.IntVar(value=0)

        # الصف الأول - البروكسي
        row1_frame = ttk.Frame(network_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        proxy_check = ttk.Checkbutton(row1_frame, text="🔒 استخدام بروكسي",
                                     variable=self.use_proxy_var,
                                     command=self.toggle_proxy)
        proxy_check.pack(side=tk.LEFT)

        self.proxy_entry = ttk.Entry(row1_frame, textvariable=self.proxy_url_var,
                                    width=30, state="disabled")
        self.proxy_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)

        # الصف الثاني - User Agent
        row2_frame = ttk.Frame(network_frame)
        row2_frame.pack(fill=tk.X, pady=10)

        ttk.Label(row2_frame, text="🌐 User Agent:").pack(side=tk.LEFT)
        ua_entry = ttk.Entry(row2_frame, textvariable=self.user_agent_var,
                            width=50, font=("Arial", 9))
        ua_entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        # زر إعادة تعيين User Agent
        reset_ua_btn = ttk.Button(row2_frame, text="🔄 افتراضي",
                                 command=self.reset_user_agent, width=10)
        reset_ua_btn.pack(side=tk.RIGHT)

        # الصف الثالث - تحديد السرعة
        row3_frame = ttk.Frame(network_frame)
        row3_frame.pack(fill=tk.X, pady=5)

        ttk.Label(row3_frame, text="⚡ تحديد السرعة (KB/s):").pack(side=tk.LEFT)
        rate_spin = ttk.Spinbox(row3_frame, from_=0, to=10000, width=8,
                               textvariable=self.rate_limit_var)
        rate_spin.pack(side=tk.LEFT, padx=(10, 20))

        ttk.Label(row3_frame, text="(0 = بلا حدود)",
                 font=("Arial", 8), foreground="gray").pack(side=tk.LEFT)

    def setup_settings_buttons(self, parent):
        """إعداد أزرار الإعدادات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=20)

        # أزرار التحكم
        save_btn = ttk.Button(buttons_frame, text="💾 حفظ الإعدادات",
                             command=self.save_settings, style="Accent.TButton")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        load_btn = ttk.Button(buttons_frame, text="📂 تحميل الإعدادات",
                             command=self.load_settings)
        load_btn.pack(side=tk.LEFT, padx=(0, 10))

        reset_btn = ttk.Button(buttons_frame, text="🔄 إعادة تعيين",
                              command=self.reset_settings)
        reset_btn.pack(side=tk.LEFT, padx=(0, 10))

        export_btn = ttk.Button(buttons_frame, text="📤 تصدير الإعدادات",
                               command=self.export_settings)
        export_btn.pack(side=tk.RIGHT, padx=(10, 0))

        import_btn = ttk.Button(buttons_frame, text="📥 استيراد الإعدادات",
                               command=self.import_settings)
        import_btn.pack(side=tk.RIGHT)

    def toggle_proxy(self):
        """تفعيل/إلغاء تفعيل البروكسي"""
        if self.use_proxy_var.get():
            self.proxy_entry.config(state="normal")
        else:
            self.proxy_entry.config(state="disabled")

    def reset_filename_pattern(self):
        """إعادة تعيين نمط اسم الملف"""
        self.custom_filename_var.set("%(uploader)s_%(title)s.%(ext)s")
        self.status_var.set("تم إعادة تعيين نمط اسم الملف")

    def reset_user_agent(self):
        """إعادة تعيين User Agent"""
        self.user_agent_var.set("")
        self.status_var.set("تم إعادة تعيين User Agent")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            import json
            settings = {
                'auto_download': self.auto_download_var.get(),
                'overwrite': self.overwrite_var.get(),
                'create_subfolder': self.create_subfolder_var.get(),
                'max_retries': self.max_retries_var.get(),
                'timeout': self.timeout_var.get(),
                'theme': self.theme_var.get(),
                'language': self.language_var.get(),
                'notifications': self.notifications_var.get(),
                'sound': self.sound_var.get(),
                'minimize_to_tray': self.minimize_to_tray_var.get(),
                'download_subtitles': self.download_subtitles_var.get(),
                'download_thumbnail': self.download_thumbnail_var.get(),
                'download_metadata': self.download_metadata_var.get(),
                'custom_filename': self.custom_filename_var.get(),
                'concurrent_downloads': self.concurrent_downloads_var.get(),
                'use_proxy': self.use_proxy_var.get(),
                'proxy_url': self.proxy_url_var.get(),
                'user_agent': self.user_agent_var.get(),
                'rate_limit': self.rate_limit_var.get(),
                'download_path': self.download_path_var.get(),
                'quality': self.quality_var.get()
            }

            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("✅ نجح", "تم حفظ الإعدادات بنجاح!")
            self.status_var.set("✅ تم حفظ الإعدادات")

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في حفظ الإعدادات: {e}")
            self.status_var.set(f"❌ خطأ في حفظ الإعدادات: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            import json
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # تطبيق الإعدادات
            self.auto_download_var.set(settings.get('auto_download', False))
            self.overwrite_var.set(settings.get('overwrite', False))
            self.create_subfolder_var.set(settings.get('create_subfolder', True))
            self.max_retries_var.set(settings.get('max_retries', 3))
            self.timeout_var.set(settings.get('timeout', 30))
            self.theme_var.set(settings.get('theme', 'النظام الافتراضي'))
            self.language_var.set(settings.get('language', 'العربية'))
            self.notifications_var.set(settings.get('notifications', True))
            self.sound_var.set(settings.get('sound', True))
            self.minimize_to_tray_var.set(settings.get('minimize_to_tray', False))
            self.download_subtitles_var.set(settings.get('download_subtitles', False))
            self.download_thumbnail_var.set(settings.get('download_thumbnail', False))
            self.download_metadata_var.set(settings.get('download_metadata', False))
            self.custom_filename_var.set(settings.get('custom_filename', '%(uploader)s_%(title)s.%(ext)s'))
            self.concurrent_downloads_var.set(settings.get('concurrent_downloads', 1))
            self.use_proxy_var.set(settings.get('use_proxy', False))
            self.proxy_url_var.set(settings.get('proxy_url', ''))
            self.user_agent_var.set(settings.get('user_agent', ''))
            self.rate_limit_var.set(settings.get('rate_limit', 0))
            self.download_path_var.set(settings.get('download_path', config.DEFAULT_DOWNLOAD_PATH))
            self.quality_var.set(settings.get('quality', 'أفضل جودة متاحة'))

            # تحديث حالة البروكسي
            self.toggle_proxy()

            messagebox.showinfo("✅ نجح", "تم تحميل الإعدادات بنجاح!")
            self.status_var.set("✅ تم تحميل الإعدادات")

        except FileNotFoundError:
            messagebox.showwarning("⚠️ تحذير", "لم يتم العثور على ملف الإعدادات")
            self.status_var.set("⚠️ لم يتم العثور على ملف الإعدادات")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في تحميل الإعدادات: {e}")
            self.status_var.set(f"❌ خطأ في تحميل الإعدادات: {e}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            # إعادة تعيين جميع المتغيرات
            self.auto_download_var.set(False)
            self.overwrite_var.set(False)
            self.create_subfolder_var.set(True)
            self.max_retries_var.set(3)
            self.timeout_var.set(30)
            self.theme_var.set("النظام الافتراضي")
            self.language_var.set("العربية")
            self.notifications_var.set(True)
            self.sound_var.set(True)
            self.minimize_to_tray_var.set(False)
            self.download_subtitles_var.set(False)
            self.download_thumbnail_var.set(False)
            self.download_metadata_var.set(False)
            self.custom_filename_var.set("%(uploader)s_%(title)s.%(ext)s")
            self.concurrent_downloads_var.set(1)
            self.use_proxy_var.set(False)
            self.proxy_url_var.set("")
            self.user_agent_var.set("")
            self.rate_limit_var.set(0)
            self.download_path_var.set(config.DEFAULT_DOWNLOAD_PATH)
            self.quality_var.set("أفضل جودة متاحة")

            # تحديث حالة البروكسي
            self.toggle_proxy()

            messagebox.showinfo("✅ نجح", "تم إعادة تعيين جميع الإعدادات!")
            self.status_var.set("✅ تم إعادة تعيين الإعدادات")

    def export_settings(self):
        """تصدير الإعدادات إلى ملف"""
        try:
            from tkinter import filedialog
            import json

            filename = filedialog.asksaveasfilename(
                title="تصدير الإعدادات",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                settings = {
                    'auto_download': self.auto_download_var.get(),
                    'overwrite': self.overwrite_var.get(),
                    'create_subfolder': self.create_subfolder_var.get(),
                    'max_retries': self.max_retries_var.get(),
                    'timeout': self.timeout_var.get(),
                    'theme': self.theme_var.get(),
                    'language': self.language_var.get(),
                    'notifications': self.notifications_var.get(),
                    'sound': self.sound_var.get(),
                    'minimize_to_tray': self.minimize_to_tray_var.get(),
                    'download_subtitles': self.download_subtitles_var.get(),
                    'download_thumbnail': self.download_thumbnail_var.get(),
                    'download_metadata': self.download_metadata_var.get(),
                    'custom_filename': self.custom_filename_var.get(),
                    'concurrent_downloads': self.concurrent_downloads_var.get(),
                    'use_proxy': self.use_proxy_var.get(),
                    'proxy_url': self.proxy_url_var.get(),
                    'user_agent': self.user_agent_var.get(),
                    'rate_limit': self.rate_limit_var.get(),
                    'download_path': self.download_path_var.get(),
                    'quality': self.quality_var.get()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("✅ نجح", f"تم تصدير الإعدادات إلى:\n{filename}")
                self.status_var.set("✅ تم تصدير الإعدادات")

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في تصدير الإعدادات: {e}")
            self.status_var.set(f"❌ خطأ في تصدير الإعدادات: {e}")

    def import_settings(self):
        """استيراد الإعدادات من ملف"""
        try:
            from tkinter import filedialog
            import json

            filename = filedialog.askopenfilename(
                title="استيراد الإعدادات",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق الإعدادات المستوردة
                self.auto_download_var.set(settings.get('auto_download', False))
                self.overwrite_var.set(settings.get('overwrite', False))
                self.create_subfolder_var.set(settings.get('create_subfolder', True))
                self.max_retries_var.set(settings.get('max_retries', 3))
                self.timeout_var.set(settings.get('timeout', 30))
                self.theme_var.set(settings.get('theme', 'النظام الافتراضي'))
                self.language_var.set(settings.get('language', 'العربية'))
                self.notifications_var.set(settings.get('notifications', True))
                self.sound_var.set(settings.get('sound', True))
                self.minimize_to_tray_var.set(settings.get('minimize_to_tray', False))
                self.download_subtitles_var.set(settings.get('download_subtitles', False))
                self.download_thumbnail_var.set(settings.get('download_thumbnail', False))
                self.download_metadata_var.set(settings.get('download_metadata', False))
                self.custom_filename_var.set(settings.get('custom_filename', '%(uploader)s_%(title)s.%(ext)s'))
                self.concurrent_downloads_var.set(settings.get('concurrent_downloads', 1))
                self.use_proxy_var.set(settings.get('use_proxy', False))
                self.proxy_url_var.set(settings.get('proxy_url', ''))
                self.user_agent_var.set(settings.get('user_agent', ''))
                self.rate_limit_var.set(settings.get('rate_limit', 0))
                self.download_path_var.set(settings.get('download_path', config.DEFAULT_DOWNLOAD_PATH))
                self.quality_var.set(settings.get('quality', 'أفضل جودة متاحة'))

                # تحديث حالة البروكسي
                self.toggle_proxy()

                messagebox.showinfo("✅ نجح", "تم استيراد الإعدادات بنجاح!")
                self.status_var.set("✅ تم استيراد الإعدادات")

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في استيراد الإعدادات: {e}")
            self.status_var.set(f"❌ خطأ في استيراد الإعدادات: {e}")

    def setup_help_tab(self, parent):
        """إعداد تبويب المساعدة"""
        help_frame = ttk.Frame(parent, padding="20")
        help_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان التبويب
        title_label = ttk.Label(help_frame, text="❓ المساعدة والدعم",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # نص المساعدة
        help_text = scrolledtext.ScrolledText(help_frame, height=20, width=70,
                                            font=("Arial", 10), wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True)

        help_content = """
🎬 برنامج تحميل فيديوهات تويتر

📖 كيفية الاستخدام:
1. انسخ رابط الفيديو من تويتر
2. الصق الرابط في حقل "رابط الفيديو" أو سيتم اكتشافه تلقائياً
3. اختر جودة الفيديو المطلوبة
4. اختر مجلد التحميل (اختياري)
5. اضغط "تحميل الفيديو"

🔗 أنواع الروابط المدعومة:
• https://twitter.com/username/status/1234567890
• https://x.com/username/status/1234567890
• https://t.co/abcdefghij

💡 نصائح مفيدة:
• البرنامج يراقب الحافظة تلقائياً ويكتشف روابط تويتر
• يمكنك معاينة معلومات الفيديو قبل التحميل
• اختر جودة أقل إذا كان التحميل بطيئاً
• تأكد من اتصال الإنترنت قبل التحميل

⚠️ استكشاف الأخطاء:
• إذا فشل التحميل: تحقق من صحة الرابط
• إذا كان التحميل بطيئاً: اختر جودة أقل
• إذا ظهر خطأ: تأكد من أن الفيديو متاح للعامة

📞 الدعم الفني:
إذا واجهت أي مشاكل، تأكد من:
1. تحديث yt-dlp إلى أحدث إصدار
2. التحقق من اتصال الإنترنت
3. إعادة تشغيل البرنامج
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def browse_folder(self):
        """اختيار مجلد التحميل"""
        folder = filedialog.askdirectory(initialdir=self.download_path_var.get())
        if folder:
            self.download_path_var.set(folder)
            self.downloader.set_download_path(folder)
            self.status_var.set(f"تم تغيير مجلد التحميل إلى: {folder}")

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def preview_video(self):
        """معاينة معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return

        self.info_text.delete(1.0, tk.END)
        self.status_var.set("جاري الحصول على المعلومات...")
        self.progress_bar.start()

        def preview_thread():
            info, error = self.downloader.get_video_info(url)

            def update_ui():
                self.progress_bar.stop()
                if error:
                    self.status_var.set(f"خطأ: {error}")
                    self.info_text.insert(tk.END, f"❌ خطأ: {error}")
                else:
                    self.status_var.set("تم الحصول على المعلومات بنجاح")
                    info_text = f"""📝 العنوان: {info['title']}
👤 الناشر: {info['uploader']}
⏱️ المدة: {info['duration']} ثانية
👀 المشاهدات: {info['view_count']:,}
❤️ الإعجابات: {info['like_count']:,}
📅 تاريخ النشر: {info['upload_date']}
🎬 عدد الصيغ المتاحة: {info['formats']}

📄 الوصف:
{info['description'][:300] if info['description'] else 'لا يوجد وصف'}..."""
                    self.info_text.insert(tk.END, info_text)

            self.root.after(0, update_ui)

        threading.Thread(target=preview_thread, daemon=True).start()

    def start_download(self):
        """بدء التحميل مع الإعدادات المتقدمة"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return

        # تحديث إعدادات التحميل الأساسية
        quality = config.VIDEO_QUALITY_OPTIONS[self.quality_var.get()]
        self.downloader.set_quality(quality)

        # تحديد مسار التحميل مع إعدادات المجلد الفرعي
        download_path = self.download_path_var.get()
        if hasattr(self, 'create_subfolder_var') and self.create_subfolder_var.get():
            # سيتم إنشاء مجلد فرعي في دالة التحميل
            pass

        self.downloader.set_download_path(download_path)

        # تطبيق الإعدادات المتقدمة على محرك التحميل
        self.apply_advanced_settings()

    def apply_advanced_settings(self):
        """تطبيق الإعدادات المتقدمة على محرك التحميل"""
        if not hasattr(self, 'download_subtitles_var'):
            return

        # تحديث خيارات yt-dlp حسب الإعدادات
        ydl_opts = {
            'format': self.downloader.quality,
            'outtmpl': os.path.join(self.downloader.download_path, self.custom_filename_var.get()),
            'writesubtitles': self.download_subtitles_var.get(),
            'writeautomaticsub': self.download_subtitles_var.get(),
            'writethumbnail': self.download_thumbnail_var.get(),
            'writeinfojson': self.download_metadata_var.get(),
            'ignoreerrors': True,
            'no_warnings': False,
            'retries': self.max_retries_var.get(),
            'socket_timeout': self.timeout_var.get(),
        }

        # إعدادات البروكسي
        if self.use_proxy_var.get() and self.proxy_url_var.get():
            ydl_opts['proxy'] = self.proxy_url_var.get()

        # إعدادات User Agent
        if self.user_agent_var.get():
            ydl_opts['http_headers'] = {'User-Agent': self.user_agent_var.get()}

        # إعدادات تحديد السرعة
        if self.rate_limit_var.get() > 0:
            ydl_opts['ratelimit'] = self.rate_limit_var.get() * 1024  # تحويل إلى bytes

        # تحديث خيارات المحرك
        self.downloader.ydl_opts = ydl_opts

        # الحصول على الرابط مرة أخرى للتأكد
        url = self.url_var.get().strip()

        # تعطيل زر التحميل وتفعيل زر الإيقاف
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_bar.start()
        self.is_downloading = True

        def download_thread():
            success, message = self.downloader.download_video(url)

            def update_ui():
                self.progress_bar.stop()
                self.download_btn.config(state="normal")
                self.stop_btn.config(state="disabled")
                self.is_downloading = False

                if success:
                    messagebox.showinfo("✅ نجح التحميل", message)
                    self.status_var.set("✅ تم التحميل بنجاح!")
                else:
                    messagebox.showerror("❌ فشل التحميل", message)
                    self.status_var.set(f"❌ خطأ: {message}")

            self.root.after(0, update_ui)

        self.download_thread = threading.Thread(target=download_thread, daemon=True)
        self.download_thread.start()

    def open_download_folder(self):
        """فتح مجلد التحميل"""
        path = self.download_path_var.get()
        if os.path.exists(path):
            os.startfile(path)  # Windows
        else:
            messagebox.showwarning("تحذير", "مجلد التحميل غير موجود")

def main():
    """تشغيل البرنامج"""
    if not TKINTER_AVAILABLE:
        print("خطأ: tkinter غير متاح على هذا النظام")
        print("يرجى استخدام سطر الأوامر: python twitter_downloader.py")
        return

    try:
        root = tk.Tk()
        TwitterDownloaderGUI(root)
        root.mainloop()
    except Exception as e:
        print(f"خطأ في تشغيل الواجهة الرسومية: {e}")
        print("يرجى استخدام سطر الأوامر: python twitter_downloader.py")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
