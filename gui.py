#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرسومية المحسنة لبرنامج تحميل فيديوهات تويتر
Enhanced Twitter Video Downloader GUI
"""

import sys
import os
import threading

# محاولة استيراد tkinter مع معالجة أفضل للأخطاء
try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    TKINTER_AVAILABLE = True
except ImportError as e:
    TKINTER_AVAILABLE = False
    print(f"❌ خطأ: tkinter غير متاح - {e}")
    print("💡 الحل: استخدم سطر الأوامر بدلاً من ذلك")

# استيراد المكتبات المطلوبة
try:
    from twitter_downloader import TwitterVideoDownloader
    import config
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    sys.exit(1)

class TwitterDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()

        # إنشاء كائن التحميل
        try:
            self.downloader = TwitterVideoDownloader()
            self.downloader.set_progress_callback(self.update_progress)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة محرك التحميل: {e}")
            return

        # متغيرات الواجهة
        self.url_var = tk.StringVar()
        self.quality_var = tk.StringVar(value="أفضل جودة متاحة")
        self.download_path_var = tk.StringVar(value=config.DEFAULT_DOWNLOAD_PATH)
        self.status_var = tk.StringVar(value="جاهز للتحميل")

        # متغيرات التحكم
        self.is_downloading = False
        self.download_thread = None

        self.setup_ui()
        self.setup_clipboard_monitor()

    def on_closing(self):
        """معالج إغلاق النافذة"""
        # إيقاف مراقبة الحافظة
        self.clipboard_monitor_active = False

        if self.is_downloading:
            if messagebox.askokcancel("تأكيد الخروج", "يتم تحميل فيديو حالياً. هل تريد الخروج؟"):
                if self.download_thread and self.download_thread.is_alive():
                    # إيقاف التحميل
                    self.is_downloading = False
                self.root.destroy()
        else:
            self.root.destroy()

    def setup_clipboard_monitor(self):
        """مراقبة الحافظة للروابط"""
        self.last_clipboard = ""
        self.clipboard_monitor_active = True
        self.monitor_clipboard()

    def monitor_clipboard(self):
        """مراقبة الحافظة بشكل دوري"""
        if not self.clipboard_monitor_active:
            return

        try:
            current_clipboard = self.root.clipboard_get()
            if (current_clipboard != self.last_clipboard and
                current_clipboard.startswith(('http://', 'https://')) and
                ('twitter.com' in current_clipboard or 'x.com' in current_clipboard or 't.co' in current_clipboard)):

                # عرض رسالة تأكيد
                if messagebox.askyesno("رابط جديد", f"تم اكتشاف رابط تويتر في الحافظة:\n\n{current_clipboard[:100]}...\n\nهل تريد استخدامه؟"):
                    self.url_var.set(current_clipboard)

                self.last_clipboard = current_clipboard
        except:
            pass

        # إعادة المراقبة كل ثانيتين إذا كانت النافذة ما زالت موجودة
        if self.clipboard_monitor_active:
            try:
                self.root.after(2000, self.monitor_clipboard)
            except:
                self.clipboard_monitor_active = False

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎬 برنامج تحميل فيديوهات تويتر")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # تعيين أيقونة النافذة (إذا كانت متاحة)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(600, 500)

        # معالج إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        # إنشاء النوت بوك للتبويبات
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب التحميل الرئيسي
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="📥 التحميل")

        # تبويب الإعدادات
        settings_tab = ttk.Frame(notebook)
        notebook.add(settings_tab, text="⚙️ الإعدادات")

        # تبويب المساعدة
        help_tab = ttk.Frame(notebook)
        notebook.add(help_tab, text="❓ المساعدة")

        # إعداد تبويب التحميل الرئيسي
        self.setup_main_tab(main_tab)

        # إعداد تبويب الإعدادات
        self.setup_settings_tab(settings_tab)

        # إعداد تبويب المساعدة
        self.setup_help_tab(help_tab)

    def setup_main_tab(self, parent):
        """إعداد تبويب التحميل الرئيسي"""
        # إطار رئيسي مع تمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان البرنامج مع أيقونة
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk.Label(title_frame, text="🎬 برنامج تحميل فيديوهات تويتر",
                               font=("Arial", 18, "bold"))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="حمل فيديوهاتك المفضلة بجودة عالية",
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(5, 0))

        # قسم إدخال الرابط
        url_frame = ttk.LabelFrame(main_frame, text="📎 رابط الفيديو", padding="15")
        url_frame.pack(fill=tk.X, pady=(0, 20))

        # إطار الرابط مع الأزرار
        url_input_frame = ttk.Frame(url_frame)
        url_input_frame.pack(fill=tk.X)

        # حقل إدخال الرابط
        self.url_entry = ttk.Entry(url_input_frame, textvariable=self.url_var,
                                  font=("Arial", 11), width=50)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # زر لصق من الحافظة
        paste_btn = ttk.Button(url_input_frame, text="📋 لصق",
                              command=self.paste_from_clipboard, width=8)
        paste_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر مسح الرابط
        clear_url_btn = ttk.Button(url_input_frame, text="🗑️ مسح",
                                  command=self.clear_url, width=8)
        clear_url_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر معاينة المعلومات
        preview_btn = ttk.Button(url_input_frame, text="👁️ معاينة",
                                command=self.preview_video, width=10)
        preview_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # نصائح للمستخدم
        tip_label = ttk.Label(url_frame,
                             text="💡 نصيحة: انسخ الرابط من تويتر وسيتم اكتشافه تلقائياً",
                             font=("Arial", 9), foreground="gray")
        tip_label.pack(pady=(10, 0))

        # قسم الإعدادات السريعة
        settings_frame = ttk.LabelFrame(main_frame, text="⚙️ إعدادات التحميل", padding="15")
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # إطار الجودة والمجلد
        settings_grid = ttk.Frame(settings_frame)
        settings_grid.pack(fill=tk.X)

        # اختيار الجودة
        ttk.Label(settings_grid, text="🎬 جودة الفيديو:").grid(row=0, column=0, sticky=tk.W, pady=5)
        quality_combo = ttk.Combobox(settings_grid, textvariable=self.quality_var,
                                   values=list(config.VIDEO_QUALITY_OPTIONS.keys()),
                                   state="readonly", width=20)
        quality_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 20), pady=5)

        # مسار التحميل
        ttk.Label(settings_grid, text="📁 مجلد التحميل:").grid(row=1, column=0, sticky=tk.W, pady=5)
        path_frame = ttk.Frame(settings_grid)
        path_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.path_entry = ttk.Entry(path_frame, textvariable=self.download_path_var,
                                   font=("Arial", 10), width=40)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        browse_btn = ttk.Button(path_frame, text="📂 تصفح", command=self.browse_folder, width=10)
        browse_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # تكوين الشبكة
        settings_grid.columnconfigure(2, weight=1)

        # قسم معلومات الفيديو
        info_frame = ttk.LabelFrame(main_frame, text="📋 معلومات الفيديو", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        self.info_text = scrolledtext.ScrolledText(info_frame, height=8, width=70,
                                                  font=("Arial", 10), wrap=tk.WORD)
        self.info_text.pack(fill=tk.BOTH, expand=True)

        # قسم التقدم والحالة
        progress_frame = ttk.LabelFrame(main_frame, text="📊 حالة التحميل", padding="15")
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        # شريط التقدم
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # رسالة الحالة
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                     font=("Arial", 10))
        self.status_label.pack()

        # قسم الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        # زر التحميل الرئيسي
        self.download_btn = ttk.Button(buttons_frame, text="⬇️ تحميل الفيديو",
                                     command=self.start_download,
                                     style="Accent.TButton")
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر إيقاف التحميل
        self.stop_btn = ttk.Button(buttons_frame, text="⏹️ إيقاف",
                                  command=self.stop_download, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر مسح الكل
        clear_all_btn = ttk.Button(buttons_frame, text="🗑️ مسح الكل",
                                  command=self.clear_all)
        clear_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر فتح مجلد التحميل
        open_folder_btn = ttk.Button(buttons_frame, text="📂 فتح المجلد",
                                    command=self.open_download_folder)
        open_folder_btn.pack(side=tk.RIGHT)

    def paste_from_clipboard(self):
        """لصق الرابط من الحافظة"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.url_var.set(clipboard_content.strip())
                self.status_var.set("تم لصق الرابط من الحافظة")
        except:
            messagebox.showwarning("تحذير", "لا يوجد محتوى في الحافظة")

    def clear_url(self):
        """مسح الرابط"""
        self.url_var.set("")
        self.status_var.set("تم مسح الرابط")

    def clear_all(self):
        """مسح جميع الحقول"""
        self.url_var.set("")
        self.info_text.delete(1.0, tk.END)
        self.status_var.set("تم مسح جميع البيانات")
        self.progress_bar.stop()

    def stop_download(self):
        """إيقاف التحميل"""
        self.is_downloading = False
        self.download_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()
        self.status_var.set("تم إيقاف التحميل")

    def setup_settings_tab(self, parent):
        """إعداد تبويب الإعدادات"""
        settings_frame = ttk.Frame(parent, padding="20")
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان التبويب
        title_label = ttk.Label(settings_frame, text="⚙️ إعدادات البرنامج",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # إعدادات متقدمة
        advanced_frame = ttk.LabelFrame(settings_frame, text="إعدادات متقدمة", padding="15")
        advanced_frame.pack(fill=tk.X, pady=(0, 20))

        # خيارات إضافية يمكن إضافتها لاحقاً
        info_label = ttk.Label(advanced_frame, text="سيتم إضافة إعدادات متقدمة في الإصدارات القادمة")
        info_label.pack()

    def setup_help_tab(self, parent):
        """إعداد تبويب المساعدة"""
        help_frame = ttk.Frame(parent, padding="20")
        help_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان التبويب
        title_label = ttk.Label(help_frame, text="❓ المساعدة والدعم",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # نص المساعدة
        help_text = scrolledtext.ScrolledText(help_frame, height=20, width=70,
                                            font=("Arial", 10), wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True)

        help_content = """
🎬 برنامج تحميل فيديوهات تويتر

📖 كيفية الاستخدام:
1. انسخ رابط الفيديو من تويتر
2. الصق الرابط في حقل "رابط الفيديو" أو سيتم اكتشافه تلقائياً
3. اختر جودة الفيديو المطلوبة
4. اختر مجلد التحميل (اختياري)
5. اضغط "تحميل الفيديو"

🔗 أنواع الروابط المدعومة:
• https://twitter.com/username/status/1234567890
• https://x.com/username/status/1234567890
• https://t.co/abcdefghij

💡 نصائح مفيدة:
• البرنامج يراقب الحافظة تلقائياً ويكتشف روابط تويتر
• يمكنك معاينة معلومات الفيديو قبل التحميل
• اختر جودة أقل إذا كان التحميل بطيئاً
• تأكد من اتصال الإنترنت قبل التحميل

⚠️ استكشاف الأخطاء:
• إذا فشل التحميل: تحقق من صحة الرابط
• إذا كان التحميل بطيئاً: اختر جودة أقل
• إذا ظهر خطأ: تأكد من أن الفيديو متاح للعامة

📞 الدعم الفني:
إذا واجهت أي مشاكل، تأكد من:
1. تحديث yt-dlp إلى أحدث إصدار
2. التحقق من اتصال الإنترنت
3. إعادة تشغيل البرنامج
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def browse_folder(self):
        """اختيار مجلد التحميل"""
        folder = filedialog.askdirectory(initialdir=self.download_path_var.get())
        if folder:
            self.download_path_var.set(folder)
            self.downloader.set_download_path(folder)
            self.status_var.set(f"تم تغيير مجلد التحميل إلى: {folder}")

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def preview_video(self):
        """معاينة معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return

        self.info_text.delete(1.0, tk.END)
        self.status_var.set("جاري الحصول على المعلومات...")
        self.progress_bar.start()

        def preview_thread():
            info, error = self.downloader.get_video_info(url)

            def update_ui():
                self.progress_bar.stop()
                if error:
                    self.status_var.set(f"خطأ: {error}")
                    self.info_text.insert(tk.END, f"❌ خطأ: {error}")
                else:
                    self.status_var.set("تم الحصول على المعلومات بنجاح")
                    info_text = f"""📝 العنوان: {info['title']}
👤 الناشر: {info['uploader']}
⏱️ المدة: {info['duration']} ثانية
👀 المشاهدات: {info['view_count']:,}
❤️ الإعجابات: {info['like_count']:,}
📅 تاريخ النشر: {info['upload_date']}
🎬 عدد الصيغ المتاحة: {info['formats']}

📄 الوصف:
{info['description'][:300] if info['description'] else 'لا يوجد وصف'}..."""
                    self.info_text.insert(tk.END, info_text)

            self.root.after(0, update_ui)

        threading.Thread(target=preview_thread, daemon=True).start()

    def start_download(self):
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return

        # تحديث إعدادات التحميل
        quality = config.VIDEO_QUALITY_OPTIONS[self.quality_var.get()]
        self.downloader.set_quality(quality)
        self.downloader.set_download_path(self.download_path_var.get())

        # تعطيل زر التحميل وتفعيل زر الإيقاف
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_bar.start()
        self.is_downloading = True

        def download_thread():
            success, message = self.downloader.download_video(url)

            def update_ui():
                self.progress_bar.stop()
                self.download_btn.config(state="normal")
                self.stop_btn.config(state="disabled")
                self.is_downloading = False

                if success:
                    messagebox.showinfo("✅ نجح التحميل", message)
                    self.status_var.set("✅ تم التحميل بنجاح!")
                else:
                    messagebox.showerror("❌ فشل التحميل", message)
                    self.status_var.set(f"❌ خطأ: {message}")

            self.root.after(0, update_ui)

        self.download_thread = threading.Thread(target=download_thread, daemon=True)
        self.download_thread.start()

    def open_download_folder(self):
        """فتح مجلد التحميل"""
        path = self.download_path_var.get()
        if os.path.exists(path):
            os.startfile(path)  # Windows
        else:
            messagebox.showwarning("تحذير", "مجلد التحميل غير موجود")
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط الفيديو")
            return
        
        # تحديث إعدادات التحميل
        quality = config.VIDEO_QUALITY_OPTIONS[self.quality_var.get()]
        self.downloader.set_quality(quality)
        self.downloader.set_download_path(self.download_path_var.get())
        
        # تعطيل زر التحميل
        self.download_btn.config(state="disabled")
        self.progress_bar.start()
        
        def download_thread():
            success, message = self.downloader.download_video(url)
            
            def update_ui():
                self.progress_bar.stop()
                self.download_btn.config(state="normal")
                
                if success:
                    messagebox.showinfo("✅ نجح التحميل", message)
                    self.status_var.set("✅ تم التحميل بنجاح!")
                else:
                    messagebox.showerror("❌ فشل التحميل", message)
                    self.status_var.set(f"❌ خطأ: {message}")
            
            self.root.after(0, update_ui)
        
        threading.Thread(target=download_thread, daemon=True).start()

def main():
    """تشغيل البرنامج"""
    if not TKINTER_AVAILABLE:
        print("خطأ: tkinter غير متاح على هذا النظام")
        print("يرجى استخدام سطر الأوامر: python twitter_downloader.py")
        return

    try:
        root = tk.Tk()
        TwitterDownloaderGUI(root)
        root.mainloop()
    except Exception as e:
        print(f"خطأ في تشغيل الواجهة الرسومية: {e}")
        print("يرجى استخدام سطر الأوامر: python twitter_downloader.py")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
