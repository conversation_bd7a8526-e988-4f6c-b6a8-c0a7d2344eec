# 📱 Twitter Video Downloader Pro

<div align="center">

![Twitter Video Downloader](https://img.shields.io/badge/Twitter-Video%20Downloader-blue?style=for-the-badge&logo=twitter)
![Version](https://img.shields.io/badge/Version-2.1-green?style=for-the-badge)
![Platform](https://img.shields.io/badge/Platform-Android-brightgreen?style=for-the-badge&logo=android)
![License](https://img.shields.io/badge/License-Open%20Source-orange?style=for-the-badge)

**🎉 Professional mobile app for downloading Twitter videos in high quality**

[📱 Download APK](#-download-apk) • [✨ Features](#-features) • [📖 Installation](#-installation) • [🔧 Build](#-build-from-source)

</div>

---

## ✨ Features

- 🎥 **Download Twitter videos** in high quality
- 🔗 **Support all Twitter URLs**: twitter.com, x.com, t.co
- 🎛️ **Choose video quality**: Best, 1080p, 720p, 480p
- 👁️ **Preview video information** before download
- 📱 **Material Design interface** optimized for mobile
- 💾 **Auto save** to Downloads folder
- ⚡ **Real-time progress bar** and status messages
- 🔒 **Privacy focused** - no data collection

## 📱 Download APK

### 🚀 Latest Release
Download the latest APK from the [Releases](../../releases) section.

**APK Info:**
- **Name**: `twitterdownloaderpro-2.1-arm64-v8a-debug.apk`
- **Size**: ~50-80 MB
- **Platform**: Android 5.0+
- **Architecture**: arm64-v8a

### 🔗 Supported URLs
- `https://twitter.com/username/status/123456789`
- `https://x.com/username/status/123456789`
- `https://t.co/abcdefghij`
- `https://mobile.twitter.com/username/status/123456789`

## 📖 Installation

### 📱 On Android Device
1. **Enable "Unknown sources"** in Android Settings > Security
2. **Download the APK** file from Releases
3. **Tap the APK** file to install
4. **Grant permissions** when prompted
5. **Launch the app** and start downloading!

### 🎯 How to Use
1. **Paste video URL** from Twitter
2. **Choose quality** by tapping the Quality button
3. **Tap Preview** to see video information
4. **Tap Download** to start downloading
5. **Find your video** in `/storage/emulated/0/Download/TwitterVideos/`

## 🔧 Build from Source

### 🌐 Method 1: GitHub Actions (Recommended)
1. **Fork this repository**
2. **GitHub Actions will automatically build APK**
3. **Download APK** from the Actions artifacts or Releases

### ☁️ Method 2: Google Colab
1. **Open** `Twitter_Video_Downloader_APK_Builder.ipynb` in Google Colab
2. **Run all cells** step by step
3. **Upload** the project files when prompted
4. **Download APK** when build completes

### 🐧 Method 3: Local Linux/WSL
```bash
# Install dependencies
sudo apt update
sudo apt install -y python3 python3-pip openjdk-8-jdk
sudo apt install -y autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev

# Install buildozer
pip3 install buildozer cython

# Clone and build
git clone <repository-url>
cd twitter-video-downloader-pro
buildozer android debug
```

## 🛡️ Security & Privacy

### 🔒 Security Features
- ✅ **Open source code** - fully reviewable
- ✅ **Local building** - build APK yourself
- ✅ **No external servers** - works locally only
- ✅ **No data collection** - no personal information gathered
- ✅ **Minimal permissions** - Internet and Storage only

### 🔐 Required Permissions
- **INTERNET**: To download videos
- **WRITE_EXTERNAL_STORAGE**: To save videos
- **READ_EXTERNAL_STORAGE**: To access saved files

## 📊 Technical Details

### 🏗️ Built With
- **Python 3.9+**
- **Kivy** - Cross-platform UI framework
- **KivyMD** - Material Design components
- **yt-dlp** - Video download engine
- **Buildozer** - Android packaging tool

### 📱 App Specifications
- **Minimum Android**: 5.0 (API 21)
- **Target Android**: Latest
- **Architecture**: arm64-v8a
- **Size**: ~50-80 MB
- **Orientation**: Portrait

## 🆘 Troubleshooting

### Common Issues
- **"yt-dlp not found"**: App needs internet connection on first run
- **"Download failed"**: Check if the Twitter URL is valid
- **"No space"**: Free up storage space on device
- **"Permission denied"**: Enable storage permissions in app settings

### 🔧 Build Issues
- **Java not found**: Install OpenJDK 8
- **Build failed**: Try `buildozer android clean` then rebuild
- **Dependencies error**: Check internet connection and try again

## 🤝 Contributing

### 🐛 Report Issues
- Describe the problem in detail
- Include your Android version
- Provide error messages if any
- Steps to reproduce the issue

### 💡 Feature Requests
- Suggest new features
- Explain the use case
- Consider implementation complexity

### 🔧 Code Contributions
- Fork the repository
- Create a feature branch
- Make your changes
- Test thoroughly
- Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **yt-dlp** team for the excellent video download library
- **Kivy** team for the cross-platform framework
- **KivyMD** team for Material Design components
- **Buildozer** team for Android packaging tools

---

<div align="center">

**🎉 Enjoy downloading your favorite Twitter videos!**

![Made with Love](https://img.shields.io/badge/Made%20with-❤️-red?style=for-the-badge)
![Python](https://img.shields.io/badge/Python-3.9+-blue?style=for-the-badge&logo=python)
![Android](https://img.shields.io/badge/Android-5.0+-green?style=for-the-badge&logo=android)

**Twitter Video Downloader Pro Team**  
*Version 2.1 - December 2024*

</div>
