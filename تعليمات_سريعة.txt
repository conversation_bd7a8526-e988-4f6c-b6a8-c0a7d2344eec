برنامج تحميل فيديوهات تويتر - تعليمات سريعة
===========================================

طرق تشغيل البرنامج:
-------------------

1. الطريقة الأسهل (للمبتدئين):
   - انقر مرتين على ملف "run.bat"
   - اختر الخيار رقم 1 للواجهة الرسومية

2. تشغيل الواجهة الرسومية مباشرة:
   - انقر مرتين على ملف "run_gui.bat"

3. من سطر الأوامر:
   - افتح موجه الأوامر في مجلد البرنامج
   - اكتب: python main.py

خطوات الاستخدام:
-----------------

1. انسخ رابط الفيديو من تويتر
2. الصق الرابط في خانة "رابط الفيديو"
3. اختر جودة الفيديو المطلوبة
4. اختر مجلد التحميل (اختياري)
5. اضغط "تحميل الفيديو"

أنواع الروابط المدعومة:
-----------------------
- https://twitter.com/username/status/1234567890
- https://x.com/username/status/1234567890  
- https://t.co/abcdefghij

نصائح مهمة:
-----------
- تأكد من اتصال الإنترنت
- تأكد من أن الفيديو متاح للعامة
- إذا فشل التحميل، جرب جودة أقل
- البرنامج يحفظ الفيديوهات في مجلد Downloads/Twitter_Videos افتراضياً

استكشاف الأخطاء:
-----------------
- إذا ظهر خطأ "Python غير موجود": ثبت Python من python.org
- إذا فشل التحميل: تحقق من صحة الرابط
- إذا كان التحميل بطيء: اختر جودة أقل

للدعم الفني:
-----------
راجع ملف README.md للتفاصيل الكاملة
