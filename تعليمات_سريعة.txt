🎬 برنامج تحميل فيديوهات تويتر المحسن - تعليمات سريعة
========================================================

🚀 طرق تشغيل البرنامج:
---------------------

1. ⭐ الطريقة الأسهل (مستحسن):
   - انقر مرتين على ملف "تشغيل_البرنامج.bat"
   - سيعمل كل شيء تلقائياً!

2. 🖥️ الواجهة الرسومية مباشرة:
   - انقر مرتين على ملف "run_gui.bat"

3. 💻 سطر الأوامر:
   - انقر مرتين على ملف "run.bat"
   - اختر الخيار رقم 2

4. 🔧 للمتقدمين:
   - افتح موجه الأوامر في مجلد البرنامج
   - اكتب: python gui.py

✨ المميزات الجديدة:
-------------------

🔍 اكتشاف تلقائي للروابط: انسخ أي رابط تويتر وسيظهر تلقائياً!
📋 لصق سريع: زر لصق مباشر من الحافظة
👁️ معاينة المعلومات: شاهد تفاصيل الفيديو قبل التحميل
🛑 إيقاف التحميل: يمكنك إيقاف التحميل في أي وقت
📂 فتح المجلد: زر مباشر لفتح مجلد التحميل

📖 خطوات الاستخدام:
--------------------

1. 📎 انسخ رابط الفيديو من تويتر
2. 🎬 سيظهر الرابط تلقائياً أو اضغط "لصق"
3. 👁️ اضغط "معاينة" لرؤية معلومات الفيديو
4. ⚙️ اختر جودة الفيديو المطلوبة
5. 📁 اختر مجلد التحميل (اختياري)
6. ⬇️ اضغط "تحميل الفيديو"

أنواع الروابط المدعومة:
-----------------------
- https://twitter.com/username/status/1234567890
- https://x.com/username/status/1234567890  
- https://t.co/abcdefghij

نصائح مهمة:
-----------
- تأكد من اتصال الإنترنت
- تأكد من أن الفيديو متاح للعامة
- إذا فشل التحميل، جرب جودة أقل
- البرنامج يحفظ الفيديوهات في مجلد Downloads/Twitter_Videos افتراضياً

استكشاف الأخطاء:
-----------------
- إذا ظهر خطأ "Python غير موجود": ثبت Python من python.org
- إذا فشل التحميل: تحقق من صحة الرابط
- إذا كان التحميل بطيء: اختر جودة أقل

للدعم الفني:
-----------
راجع ملف README.md للتفاصيل الكاملة
